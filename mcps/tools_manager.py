"""
MCP Tools Manager for the ReAct Agent Framework

This module provides a bridge between the ReAct agent and FastMCP tools,
allowing the agent to call async FastMCP tools synchronously.
"""
import asyncio
import inspect
from typing import Dict, Any, Callable, List
from dataclasses import dataclass

from logger.get_logger import log
from mcps.tools.local_tools import (
    math_calculator,
    get_current_date,
    get_current_time,
    get_current_datetime,
    search_web,
    get_weather
)
from mcps.tools.remote_tools import call_external_api


@dataclass
class MCPToolWrapper:
    """Wrapper for MCP tools to provide consistent interface for ReAct agent."""
    name: str
    func: Callable
    description: str
    is_async: bool = True


class MCPToolsManager:
    """
    MCP Tools Manager that provides FastMCP tools to the ReAct agent.
    
    Handles the async-to-sync bridge needed for the current ReAct agent
    implementation while maintaining the FastMCP tool structure.
    """
    
    def __init__(self):
        """Initialize the MCP tools manager."""
        self.tools = {}
        self._initialize_tools()
    
    def _initialize_tools(self):
        """Initialize all available FastMCP tools."""
        # Get all FastMCP tools
        fastmcp_tools = self._get_fastmcp_tools()
        
        # Wrap tools for ReAct agent compatibility
        for name, tool_func in fastmcp_tools.items():
            is_async = inspect.iscoroutinefunction(tool_func)
            description = self._extract_description(tool_func)
            
            self.tools[name] = MCPToolWrapper(
                name=name,
                func=self._create_sync_wrapper(tool_func) if is_async else tool_func,
                description=description,
                is_async=is_async
            )
    
    def _get_fastmcp_tools(self) -> Dict[str, Callable]:
        """Get all FastMCP tools."""
        return {
            # Local tools
            'math_calculator': math_calculator,
            'get_current_date': get_current_date,
            'get_current_time': get_current_time,
            'get_current_datetime': get_current_datetime,
            'search_web': search_web,
            'get_weather': get_weather,
            
            # Remote tools
            'call_external_api': call_external_api,
        }
    
    def _extract_description(self, func: Callable) -> str:
        """Extract description from function docstring."""
        if hasattr(func, '__doc__') and func.__doc__:
            # Get first line of docstring as description
            lines = func.__doc__.strip().split('\n')
            return lines[0].strip()
        return f"FastMCP Tool: {getattr(func, '__name__', 'unknown')}"
    
    def _create_sync_wrapper(self, fastmcp_tool) -> Callable:
        """Create a synchronous wrapper for FastMCP tools."""
        def sync_wrapper(input_text: str = "") -> str:
            """Synchronous wrapper for FastMCP tools."""
            try:
                # Handle FastMCP FunctionTool objects
                if hasattr(fastmcp_tool, 'run'):
                    # Use the run method for FastMCP tools
                    result = asyncio.run(fastmcp_tool.run(input_text))
                    return str(result)
                elif hasattr(fastmcp_tool, 'fn'):
                    # Use the underlying function
                    if inspect.iscoroutinefunction(fastmcp_tool.fn):
                        result = asyncio.run(fastmcp_tool.fn(input_text))
                    else:
                        result = fastmcp_tool.fn(input_text)
                    return str(result)
                else:
                    # Fallback for regular async functions
                    loop = None
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # If loop is already running, create a new thread
                            import concurrent.futures

                            def run_in_thread():
                                new_loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(new_loop)
                                try:
                                    return new_loop.run_until_complete(fastmcp_tool(input_text))
                                finally:
                                    new_loop.close()

                            with concurrent.futures.ThreadPoolExecutor() as executor:
                                future = executor.submit(run_in_thread)
                                return future.result(timeout=30)  # 30 second timeout
                        else:
                            return loop.run_until_complete(fastmcp_tool(input_text))
                    except RuntimeError:
                        # No event loop, create a new one
                        return asyncio.run(fastmcp_tool(input_text))
            except Exception as e:
                return f"Error executing FastMCP tool: {str(e)}"

        return sync_wrapper
    
    def get_tools(self) -> Dict[str, MCPToolWrapper]:
        """Get all available MCP tools."""
        return self.tools
    
    def get_tool_names(self) -> List[str]:
        """Get list of all tool names."""
        return list(self.tools.keys())
    
    def get_tool_descriptions(self) -> str:
        """Get formatted descriptions of all tools."""
        descriptions = []
        for name, tool in self.tools.items():
            descriptions.append(f"- {name}: {tool.description}")
        return "\n".join(descriptions)
    
    @log
    def execute_tool(self, tool_name: str, tool_input: str) -> str:
        """
        Execute a tool by name with the given input.
        
        Args:
            tool_name: Name of the tool to execute
            tool_input: Input to pass to the tool
            
        Returns:
            Tool execution result as string
        """
        if tool_name not in self.tools:
            return f"Error: Tool '{tool_name}' not found. Available tools: {list(self.tools.keys())}"
        
        try:
            tool = self.tools[tool_name]
            result = tool.func(tool_input)
            return str(result)
        except Exception as e:
            return f"Error executing tool '{tool_name}': {str(e)}"
