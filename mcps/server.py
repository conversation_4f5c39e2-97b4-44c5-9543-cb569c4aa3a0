from fastmcp import FastMCP
from mcp.tools.local_tools import local_tool_example
from mcp.tools.remote_tools import call_external_api
from mcp.resources.external_resources import get_external_data

# TODO: this should act as a starting point for all tools
# Create FastMCP instance
mcp = FastMCP(name = "ReactAgentTools")

# Register local tool
mcp.tool(local_tool_example)

# Register URL-based tool
mcp.tool(call_external_api)

# Register resource
mcp.resource("externalapi://data/{item_id}")(get_external_data)

if __name__ == "__main__":
    mcp.run()
