#!/usr/bin/env python3
"""
Model Comparison Test Script

This script tests the new Finance-Llama-8B model vs the previous model
to evaluate response quality and ReAct pattern adherence.
"""
import os
import time
from dotenv import load_dotenv

if os.path.exists(".env"):
    load_dotenv()

from config.config import Config
from models import get_model_provider

def test_model_quality():
    """Test the model's ability to follow ReAct pattern and provide good responses."""
    
    print("🔍 TESTING FINANCE-LLAMA-8B MODEL QUALITY")
    print("=" * 60)
    
    # Initialize components
    config = Config()
    model_provider = get_model_provider(config.model_provider)
    llm = model_provider.get_llm()
    
    # Test cases with expected behaviors
    test_cases = [
        {
            "name": "Date Query",
            "prompt": """You are a helpful AI assistant. Follow the ReAct pattern EXACTLY.

AVAILABLE TOOLS:
- get_current_date: Get the current date in YYYY-MM-DD format.

CRITICAL RULES:
- Start with "Thought:"
- Use tools to get real information
- NEVER make up data
- Give ONE action only

FORMAT:
Thought: [your reasoning]
Action: [tool name OR "Final Answer"]
Action Input: [input for tool OR final answer]

Step 1: Thought: The user wants today's date. I need to use the get_current_date tool.
Action: get_current_date
Action Input: 

Step 2 (after getting tool result): Thought: I got the date from the tool. Now I can provide a proper answer.
Action: Final Answer
Action Input: Today's date is [the date from the tool].

QUESTION: What's today's date?

Response:""",
            "expected_patterns": ["Thought:", "Action:", "get_current_date"],
            "avoid_patterns": ["2023", "2024", "I don't know"]
        },
        {
            "name": "Math Query", 
            "prompt": """You are a helpful AI assistant. Follow the ReAct pattern EXACTLY.

AVAILABLE TOOLS:
- math_calculator: Perform mathematical calculations safely.

CRITICAL RULES:
- Start with "Thought:"
- Use tools to get real information
- NEVER make up data
- Give ONE action only

FORMAT:
Thought: [your reasoning]
Action: [tool name OR "Final Answer"]
Action Input: [input for tool OR final answer]

Step 1: Thought: The user wants me to calculate. I need to use the math_calculator tool.
Action: math_calculator
Action Input: 15 * 23

Step 2 (after getting tool result): Thought: I got the calculation result. Now I can provide the final answer.
Action: Final Answer
Action Input: The answer to 15 * 23 is [result from tool].

QUESTION: Calculate 15 * 23

Response:""",
            "expected_patterns": ["Thought:", "Action:", "math_calculator", "15 * 23"],
            "avoid_patterns": ["345", "I calculate", "The answer is 345"]
        },
        {
            "name": "Time Query",
            "prompt": """You are a helpful AI assistant. Follow the ReAct pattern EXACTLY.

AVAILABLE TOOLS:
- get_current_time: Get the current time in HH:MM:SS format.

CRITICAL RULES:
- Start with "Thought:"
- Use tools to get real information
- NEVER make up data
- Give ONE action only

FORMAT:
Thought: [your reasoning]
Action: [tool name OR "Final Answer"]
Action Input: [input for tool OR final answer]

Step 1: Thought: The user wants the current time. I need to use the get_current_time tool.
Action: get_current_time
Action Input: 

Step 2 (after getting tool result): Thought: I got the time from the tool. Now I can provide a proper answer.
Action: Final Answer
Action Input: The current time is [the time from the tool].

QUESTION: What time is it?

Response:""",
            "expected_patterns": ["Thought:", "Action:", "get_current_time"],
            "avoid_patterns": ["12:", "13:", "14:", "15:", "16:", "AM", "PM"]
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"TEST {i}: {test_case['name']}")
        print('='*60)
        
        try:
            start_time = time.time()
            response = llm.invoke(test_case['prompt'])
            response_time = time.time() - start_time
            
            # Extract text content from response
            if hasattr(response, 'content'):
                response_text = response.content
            elif hasattr(response, 'text'):
                response_text = response.text
            else:
                response_text = str(response)
            
            print(f"Response Time: {response_time:.2f}s")
            print(f"Response Length: {len(response_text)} chars")
            print(f"\nRaw Response:\n{repr(response_text)}")
            print(f"\nFormatted Response:\n{response_text}")
            
            # Analyze response quality
            score = 0
            max_score = len(test_case['expected_patterns']) + len(test_case['avoid_patterns'])
            
            # Check for expected patterns
            for pattern in test_case['expected_patterns']:
                if pattern.lower() in response_text.lower():
                    score += 1
                    print(f"✅ Found expected pattern: '{pattern}'")
                else:
                    print(f"❌ Missing expected pattern: '{pattern}'")
            
            # Check for patterns to avoid
            for pattern in test_case['avoid_patterns']:
                if pattern.lower() not in response_text.lower():
                    score += 1
                    print(f"✅ Correctly avoided: '{pattern}'")
                else:
                    print(f"⚠️ Found unwanted pattern: '{pattern}'")
            
            quality_score = (score / max_score) * 100
            print(f"\n📊 Quality Score: {quality_score:.1f}% ({score}/{max_score})")
            
            results.append({
                'name': test_case['name'],
                'score': quality_score,
                'response_time': response_time,
                'response_length': len(response_text),
                'response': response_text
            })
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            results.append({
                'name': test_case['name'],
                'score': 0,
                'error': str(e)
            })
    
    # Summary
    print(f"\n{'='*60}")
    print("📈 MODEL PERFORMANCE SUMMARY")
    print('='*60)
    
    total_score = 0
    successful_tests = 0
    total_time = 0
    
    for result in results:
        if 'error' not in result:
            print(f"{result['name']}: {result['score']:.1f}% ({result['response_time']:.2f}s)")
            total_score += result['score']
            total_time += result['response_time']
            successful_tests += 1
        else:
            print(f"{result['name']}: FAILED - {result['error']}")
    
    if successful_tests > 0:
        avg_score = total_score / successful_tests
        avg_time = total_time / successful_tests
        
        print(f"\n🎯 Overall Performance:")
        print(f"   Average Quality Score: {avg_score:.1f}%")
        print(f"   Average Response Time: {avg_time:.2f}s")
        print(f"   Success Rate: {successful_tests}/{len(test_cases)} tests")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        if avg_score >= 80:
            print("   ✅ Model performs excellently for ReAct pattern")
        elif avg_score >= 60:
            print("   ⚠️ Model performs adequately but could be improved")
            print("   💡 Consider prompt engineering or larger model")
        else:
            print("   ❌ Model struggles with ReAct pattern")
            print("   💡 Recommend using a larger model (13B+ parameters)")
        
        if avg_time > 5:
            print("   ⏱️ Response time is slow, consider optimization")
        elif avg_time < 1:
            print("   ⚡ Excellent response time")
    
    return results

if __name__ == "__main__":
    test_model_quality()
