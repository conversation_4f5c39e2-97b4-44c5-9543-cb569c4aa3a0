#!/usr/bin/env python3
"""
Debug script to test LLM responses directly
"""
import os
from dotenv import load_dotenv

if os.path.exists(".env"):
    load_dotenv()

from config.config import Config
from models import get_model_provider

def test_llm_response(question="What's today's date?"):
    """Test direct LLM response to see what it's actually returning."""

    # Initialize components
    config = Config()
    model_provider = get_model_provider(config.model_provider)
    llm = model_provider.get_llm()

    # Test prompt similar to what ReAct agent uses
    prompt = f"""You are a helpful AI assistant that uses tools to answer questions. Follow the ReAct pattern exactly.

AVAILABLE TOOLS:
- get_current_date: Get the current date in YYYY-MM-DD format.
- math_calculator: Perform mathematical calculations safely.

CRITICAL INSTRUCTIONS:
1. If you have the information needed to answer the user, use "Final Answer"
2. Only use tools when you need to get new information
3. After using a tool successfully, provide the Final Answer

RESPONSE FORMAT (follow exactly):
Thought: [Your reasoning about what to do next]
Action: [exact tool name from the list above OR "Final Answer"]
Action Input: [input for the tool OR your final answer]

USER QUESTION: {question}

Respond in the exact format above:"""
    
    print("🔍 TESTING LLM RESPONSE")
    print("=" * 50)
    print(f"Prompt:\n{prompt}")
    print("\n" + "=" * 50)
    
    # Get LLM response
    response = llm.invoke(prompt)
    
    # Extract text content from response
    if hasattr(response, 'content'):
        response_text = response.content
    elif hasattr(response, 'text'):
        response_text = response.text
    else:
        response_text = str(response)
    
    print(f"Raw LLM Response:\n{repr(response_text)}")
    print(f"\nFormatted LLM Response:\n{response_text}")
    print("=" * 50)

    # Test the parsing logic
    import re

    print("\n🔍 TESTING PARSING LOGIC")
    print("=" * 50)

    # Extract thought
    thought_match = re.search(r'Thought:\s*(.+?)(?=\nAction:|$)', response_text, re.DOTALL | re.IGNORECASE)
    thought = thought_match.group(1).strip() if thought_match else "Continuing reasoning process"
    print(f"Parsed Thought: '{thought}'")

    # Extract action
    action_match = re.search(r'Action:\s*(.+?)(?=\nAction Input:|$)', response_text, re.DOTALL | re.IGNORECASE)
    action = action_match.group(1).strip() if action_match else "Final Answer"
    print(f"Parsed Action: '{action}'")

    # Extract action input
    input_match = re.search(r'Action Input:\s*(.+?)$', response_text, re.DOTALL | re.IGNORECASE)
    action_input = input_match.group(1).strip() if input_match else ""
    print(f"Parsed Action Input: '{action_input}'")

    # Clean up action and input
    action = action.replace('\n', ' ').strip()
    action_input = action_input.replace('\n', ' ').strip()

    print(f"Cleaned Action: '{action}'")
    print(f"Cleaned Action Input: '{action_input}'")

    # Determine action type
    if action.lower() == "final answer" or "final answer" in action.lower():
        print("Action Type: FINAL_ANSWER")
        final_answer = action_input if action_input else "I have completed the task."
        print(f"Final Answer: '{final_answer}'")
    else:
        print("Action Type: TOOL_CALL")
        print(f"Tool: '{action}', Input: '{action_input}'")

    return response_text

if __name__ == "__main__":
    # Test multiple questions
    questions = [
        "What's today's date?",
        "Calculate 10 + 5",
        "What time is it?"
    ]

    for i, question in enumerate(questions, 1):
        print(f"\n{'='*60}")
        print(f"TEST {i}: {question}")
        print('='*60)
        test_llm_response(question)
        print("\n")
