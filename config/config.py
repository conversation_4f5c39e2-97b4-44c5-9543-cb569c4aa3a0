"""
Configuration management for the AI agent.
"""
import os
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

class Config(BaseModel):
    """Configuration for the AI agent."""

    # Model configuration
    model_provider: str = Field(
        default_factory=lambda: os.getenv("MODEL_PROVIDER", "llamacpp")
    )
    model_name: str = Field(
        default_factory=lambda: os.getenv("MODEL_NAME", "llama")
    )

    # Tool configuration
    enabled_tools: List[str] = Field(
        default_factory=lambda: os.getenv("ENABLED_TOOLS", "search,math").split(",")
    )

    # Agent configuration
    verbose: bool = Field(
        default_factory=lambda: os.getenv("VERBOSE", "False").lower() == "true"
    )

    # Container configuration
    data_dir: str = Field(
        default_factory=lambda: os.getenv("DATA_DIR", "./data")
    )
    log_level: str = Field(
        default_factory=lambda: os.getenv("LOG_LEVEL", "INFO")
    )

    # API Keys and Endpoints
    openai_api_key: Optional[str] = Field(
        default_factory=lambda: os.getenv("OPENAI_API_KEY")
    )
    anthropic_api_key: Optional[str] = Field(
        default_factory=lambda: os.getenv("ANTHROPIC_API_KEY")
    )
    llamacpp_api_url: str = Field(
        default_factory=lambda: os.getenv("LLAMACPP_API_URL", "http://172.16.0.111:11111/completion")
    )

    def get_model_config(self) -> Dict[str, Any]:
        """Get model-specific configuration."""
        # TODO: wont be using models other than llamacpp
        if self.model_provider == "openai":
            return {
                "model": self.model_name,
                "temperature": float(os.getenv("TEMPERATURE", "0.7")),
            }
        elif self.model_provider == "anthropic":
            return {
                "model": self.model_name,
                "temperature": float(os.getenv("TEMPERATURE", "0.7")),
            }
        elif self.model_provider == "llamacpp":
            return {
                "temperature": float(os.getenv("TEMPERATURE", "0.7")),
                "max_tokens": int(os.getenv("MAX_TOKENS", "2048")),
            }
        else:
            raise ValueError(f"Unsupported model provider: {self.model_provider}")
