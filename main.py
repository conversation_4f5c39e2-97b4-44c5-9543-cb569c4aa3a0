#!/usr/bin/env python3
"""
ReAct Agent Framework - Main Application

Modular ReAct (Reasoning + Acting) agent implementation with:
- Clean architecture for easy extensibility
- Pluggable components (LLM, tools, memory)
- Interactive and demo modes
- Comprehensive error handling
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables
if os.path.exists(".env"):
    load_dotenv()

from config.config import Config
from models import get_model_provider
from tools import ReActTools
from memory import ReActMemoryManager
from core import ReActAgent
from logger.get_logger import logger


class ReActFramework:
    """
    Main ReAct Framework class providing modular initialization and execution.
    
    Extensible design allows easy swapping of:
    - LLM providers (via models module)
    - Tools (via tools module)
    - Memory backends (via memory module)
    """
    
    def __init__(self):
        """Initialize the ReAct framework."""
        self.config = None
        self.llm = None
        self.tools = None
        self.memory_manager = None
        self.agent = None
        
    def initialize(self) -> bool:
        """
        Initialize all framework components.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            print("🚀 INITIALIZING REACT AGENT FRAMEWORK")
            print("=" * 60)
            
            # Initialize configuration
            self.config = Config()
            print(f"✅ Configuration loaded: {self.config.model_provider}")
            
            # Initialize LLM provider
            model_provider = get_model_provider(self.config.model_provider)
            self.llm = model_provider.get_llm()
            print(f"✅ LLM initialized: {self.config.model_provider}")
            
            # Initialize tools
            tools_manager = ReActTools()
            self.tools = tools_manager.get_tools()
            print(f"✅ Tools loaded: {tools_manager.get_tool_names()}")
            
            # Initialize memory manager
            self.memory_manager = ReActMemoryManager(
                db_path="data/react_memory.db",
                max_tokens=3500
            )
            print("✅ Memory manager initialized")
            
            # Initialize ReAct agent
            self.agent = ReActAgent(
                llm=self.llm,
                tools=self.tools,
                memory_manager=self.memory_manager,
                max_iterations=5
            )
            print("✅ ReAct agent initialized")
            
            return True
            
        except Exception as e:
            print(f"❌ Initialization failed: {e}")
            logger.error(f"Initialization error: {e}")
            return False
    
    def run_interactive(self):
        """Run the framework in interactive mode."""
        
        print("\n" + "=" * 60)
        print("🤖 REACT AGENT - INTERACTIVE MODE")
        print("=" * 60)
        print("Features:")
        print("• Visible reasoning process (Thought → Action → Observation)")
        print("• Automatic tool selection and execution")
        print("• Conversation memory with context")
        print("• Modular, extensible architecture")
        print()
        print("Commands:")
        print("• 'exit' or 'quit' - Exit the application")
        print("• '!clear' - Clear conversation memory")
        print("• '!stats' - Show memory statistics")
        print("• '!tools' - List available tools")
        print("• '!help' - Show this help")
        print("=" * 60)
        
        while True:
            try:
                user_input = input("\n💬 You: ").strip()
                
                # Handle special commands
                if user_input.lower() in ['exit', 'quit', 'bye']:
                    print("👋 Goodbye!")
                    break
                    
                elif user_input.lower() == '!clear':
                    self.memory_manager.clear()
                    continue
                    
                elif user_input.lower() == '!stats':
                    stats = self.memory_manager.get_stats()
                    print("\n📊 Memory Statistics:")
                    for key, value in stats.items():
                        print(f"  {key}: {value}")
                    continue
                    
                elif user_input.lower() == '!tools':
                    tools_manager = ReActTools()
                    print("\n🔧 Available Tools:")
                    print(tools_manager.get_tool_descriptions())
                    continue
                    
                elif user_input.lower() == '!help':
                    self._show_help()
                    continue
                    
                elif not user_input:
                    print("❓ Please enter a question or command.")
                    continue
                
                # Process user input with ReAct agent
                response = self.agent.process(user_input)
                
                print(f"\n🤖 Agent: {response}")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
                
            except Exception as e:
                print(f"❌ Error: {e}")
                logger.error(f"Interactive mode error: {e}")
    
    def run_demo(self):
        """Run demonstration scenarios."""
        
        print("\n" + "=" * 60)
        print("🎯 REACT AGENT DEMONSTRATION")
        print("=" * 60)
        
        demo_scenarios = [
            {
                "name": "Date Query",
                "input": "What's today's date?",
                "expected": "Should use get_current_date tool"
            },
            {
                "name": "Time Query", 
                "input": "What time is it?",
                "expected": "Should use get_current_time tool"
            },
            {
                "name": "Math Calculation",
                "input": "Calculate 15 * 23",
                "expected": "Should use calculate tool"
            },
            {
                "name": "Complex Math",
                "input": "What is 5 + 3 * 2?",
                "expected": "Should use calculate tool with proper order of operations"
            },
            {
                "name": "Web Search",
                "input": "What's the current Bitcoin price?",
                "expected": "Should use search_web tool"
            }
        ]
        
        for i, scenario in enumerate(demo_scenarios, 1):
            print(f"\n{'='*60}")
            print(f"🎯 DEMO {i}: {scenario['name']}")
            print(f"Input: {scenario['input']}")
            print(f"Expected: {scenario['expected']}")
            print('='*60)
            
            try:
                response = self.agent.process(scenario['input'])
                print(f"\n✅ Final Result: {response}")
                
            except Exception as e:
                print(f"❌ Demo failed: {e}")
            
            if i < len(demo_scenarios):
                input("\nPress Enter to continue to next demo...")
        
        print(f"\n{'='*60}")
        print("🎉 DEMONSTRATION COMPLETED!")
        print("The ReAct agent successfully:")
        print("• Showed visible reasoning process")
        print("• Used appropriate tools for each query")
        print("• Maintained conversation memory")
        print("• Followed the modular ReAct architecture")
        print(f"{'='*60}")
    
    def _show_help(self):
        """Show detailed help information."""
        print("\n📋 REACT AGENT HELP")
        print("-" * 30)
        print("The ReAct agent follows this flow:")
        print("1. Receives your input + loads conversation memory")
        print("2. Reasons about what action to take")
        print("3. Either uses a tool or provides final answer")
        print("4. If tool used: observes result and continues reasoning")
        print("5. Updates memory and provides final response")
        print()
        print("🔧 Available Tools:")
        tools_manager = ReActTools()
        print(tools_manager.get_tool_descriptions())
        print()
        print("💡 Example queries:")
        print("• 'What's today's date?' - Uses date tool")
        print("• 'Calculate 15 * 23' - Uses math tool")
        print("• 'What's the Bitcoin price?' - Uses search tool")
        print("• 'Hello, how are you?' - Direct response")


def main():
    """Main application entry point."""
    
    try:
        # Initialize framework
        framework = ReActFramework()
        
        if not framework.initialize():
            print("❌ Failed to initialize framework")
            sys.exit(1)
        
        # Choose mode
        print("\n🎯 Choose Mode:")
        print("1. Interactive Mode (chat with the agent)")
        print("2. Demo Mode (see predefined scenarios)")
        print("3. Exit")
        
        while True:
            choice = input("\nEnter choice (1-3): ").strip()
            
            if choice == "1":
                framework.run_interactive()
                break
            elif choice == "2":
                framework.run_demo()
                break
            elif choice == "3":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please enter 1, 2, or 3.")
    
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Application error: {e}")
        logger.error(f"Main application error: {e}")


if __name__ == "__main__":
    main()
