# 🚀 DABOT OHLC - Multi-Source Cryptocurrency Data Aggregation API

[![Python](https://img.shields.io/badge/Python-3.10+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100+-green.svg)](https://fastapi.tiangolo.com)
[![SQLite](https://img.shields.io/badge/Database-SQLite-lightblue.svg)](https://sqlite.org)

A robust, production-ready API for aggregating OHLC (Open, High, Low, Close) cryptocurrency data from multiple sources with intelligent caching, strict data validation, and comprehensive error handling.

## 🌟 Features

- **Multi-Source Aggregation**: Combines data from Kraken, Bitstamp, and Coindesk with automatic source discovery
- **Equal-Weight Averaging**: OHLC data averaged across sources, volumes summed
- **Intelligent Caching**: Aggregated database acts as high-performance cache layer
- **100% Completeness Requirement**: Only sources with complete data participate in aggregation
- **Graceful Error Handling**: System continues working even if some sources fail
- **Source Transparency**: Detailed metadata about which sources contributed to each response
- **Automatic Discovery**: Dynamically discovers and integrates new data sources
- **Multi-Layer Validation**: Cache validation (triggers refresh) vs final validation (blocks response)
- **Comprehensive Error Tracking**: Detailed error reporting per source and aggregation step

## 📊 Architecture Overview

### System Flow Diagram

```mermaid
graph TD
    A[Client Request] --> B[Endpoint: get_candles / get_range]
    B --> C{Step 1: Check Aggregated DB}
    C -->|Valid Data Found| D[Return Cached Data]
    C -->|Validation Failed| E[Step 2: Try Source Aggregation]
    E --> F{100% Complete Sources?}
    F -->|Yes| G[Aggregate & Validate]
    F -->|No| H[Step 3: Refresh from APIs]
    G -->|Valid| I[Return Aggregated Data]
    G -->|Invalid| H
    H --> J[Step 4: Re-aggregate with Fresh Data]
    J -->|Success| K[Return Final Data]
    J -->|Failed| L[Return Error Response]

    style D fill:#90EE90
    style I fill:#87CEEB
    style K fill:#87CEEB
    style L fill:#FFB6C1
    style H fill:#FFA500
```

### Detailed Processing Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant E as Endpoint
    participant A as Aggregated DB
    participant S as Source DBs
    participant API as External APIs

    C->>E: GET /btc-usd/h1/24

    Note over E: Step 1: Cache Check
    E->>A: Query aggregated database
    E->>E: Validate data (freshness, completeness, timestamps)

    alt Cache Hit (Valid Data)
        A-->>E: Valid cached data
        E-->>C: Return with cache_hit=true
    else Cache Validation Failed
        Note over E: Step 2: Source Aggregation
        E->>S: Query all source databases
        E->>E: Validate each source (100% completeness required)
        E->>E: Exclude incomplete sources

        alt Sources with 100% Complete Data
            E->>E: Aggregate complete sources only
            E->>A: Store aggregated data
            E-->>C: Return with refreshed_from_apis=false
        else No Complete Sources
            Note over E: Step 3: API Refresh
            E->>API: Fetch fresh data from all sources
            API-->>E: Return fresh data
            E->>S: Store in source databases

            Note over E: Step 4: Re-aggregation
            E->>S: Query refreshed source databases
            E->>E: Validate and aggregate
            E->>A: Store final aggregated data
            E-->>C: Return with refreshed_from_apis=true
        end
    end
```

## 🏗️ Data Flow & Validation

### 1. Four-Step Processing Pipeline
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Step 1:       │───▶│   Step 2:       │───▶│   Step 3:       │───▶│   Step 4:       │
│ Cache Check     │    │ Source Agg.     │    │ API Refresh     │    │ Re-aggregation  │
│ (Aggregated DB) │    │ (Source DBs)    │    │ (External APIs) │    │ (Fresh Data)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. Validation Strategy
```
Cache Validation (Step 1):
├── Structural Issues → Block immediately
│   ├── DataFrame is None/empty
│   └── Invalid data types
└── Data Quality Issues → Trigger refresh
    ├── Wrong number of candles
    ├── Data not fresh
    └── Missing timestamps

Final Validation (Steps 2-4):
└── All Issues → Block response
    ├── Structural problems
    ├── Completeness failures
    └── Missing timestamps
```

### 3. Source Selection & Aggregation
```
┌─────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Kraken    │───▶│                 │    │                 │
├─────────────┤    │ 100% Complete   │───▶│  Equal-Weight   │
│  Bitstamp   │───▶│ Sources Only    │    │   Averaging     │
├─────────────┤    │                 │    │                 │
│  Coindesk   │───▶│ (Exclude        │    │ Volume Summing  │
└─────────────┘    │  Incomplete)    │    │                 │
                   └─────────────────┘    └─────────────────┘
```

### 4. Database Structure
```
app/db/data/
├── aggregated/           # Cache layer (endpoints access this only)
│   ├── btc-usd.db       # Tables: m1, m15, h1, h4, d1
│   ├── eth-usd.db       # Separate DB per currency pair
│   └── eth-btc.db       # Contains aggregated data from multiple sources
├── kraken/              # Source databases (individual source data)
│   ├── btc-usd.db
│   └── eth-usd.db
├── bitstamp/
│   ├── btc-usd.db
│   └── eth-usd.db
└── coindesk/
    └── btc-usd.db       # Each source stores raw data separately
```

### 5. Aggregation Algorithm Details
```
For each timestamp with data from multiple sources:

OHLC Values (Equal-Weight Averaging):
• Open = (Source1_Open + Source2_Open + ... + SourceN_Open) / N
• High = (Source1_High + Source2_High + ... + SourceN_High) / N
• Low = (Source1_Low + Source2_Low + ... + SourceN_Low) / N
• Close = (Source1_Close + Source2_Close + ... + SourceN_Close) / N

Volume (Summing):
• Volume = Source1_Volume + Source2_Volume + ... + SourceN_Volume

Requirement: Only sources with 100% complete data participate
Result: High-quality aggregated data with combined liquidity
```

## 🚀 Quick Start

### Prerequisites
- Python 3.10+
- Virtual environment (recommended)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd dabot-ohlc
```

2. **Set up virtual environment**
```bash
python -m venv .venv-dabot-ohlc
source .venv-dabot-ohlc/bin/activate  # On Windows: .venv-dabot-ohlc\Scripts\activate
```

3. **Install dependencies**
```bash
pip install -r requirements.txt
```

4. **Run the server**
```bash
python app/main.py
```

The API will be available at `http://localhost:8000`

### API Documentation
Visit `http://localhost:8000/docs` for interactive API documentation.

## 📡 API Endpoints

### Get Candles
```http
GET /{currency_pair}/{timeframe}/{candles}
```

**Parameters:**
- `currency_pair`: Trading pair (e.g., `btc-usd`, `eth-usd`)
- `timeframe`: Time interval (`m1`, `m5`, `m15`, `h1`, `h4`, `d1`)
- `candles`: Number of candles to retrieve

**Example:**
```bash
curl http://localhost:8000/btc-usd/h1/24
```

### Get Range
```http
GET /{currency_pair}/{timeframe}/{from_date}/{to_date}
```

**Parameters:**
- `currency_pair`: Trading pair (e.g., `btc-usd`)
- `timeframe`: Time interval (`m1`, `m5`, `m15`, `h1`, `h4`, `d1`)
- `from_date`: Start date in format `ddmmyyyy`
- `to_date`: End date in format `ddmmyyyy` or `now`

**Example:**
```bash
curl http://localhost:8000/btc-usd/h1/01012024/31012024
```

### Key Differences Between Endpoints

Both endpoints follow the same 4-step processing flow but with different parameters:

| Aspect | get_candles | get_range |
|--------|-------------|-----------|
| **Parameters** | `/{currency_pair}/{timeframe}/{candles}` | `/{currency_pair}/{timeframe}/{from_date}/{to_date}` |
| **Data Scope** | Last N candles from current time | Specific date range |
| **Validation** | Freshness validation included | Historical data, less strict freshness |
| **Metadata** | Standard aggregation metadata | Includes `date_range` field |
| **Use Case** | Recent data analysis | Historical data analysis |

Both endpoints return the same response structure with identical error handling and source transparency.

## 📋 Response Format

### Successful Response (Cache Hit)
```json
{
  "sources_used": ["aggregated_cache"],
  "sources_failed": [],
  "cache_hit": true,
  "refreshed_from_apis": false,
  "metadata": {
    "validation_passed": true,
    "timestamp": 1749653696
  },
  "data": [
    {
      "timestamp": 1749204000,
      "date": "2025-06-06T12:00:00Z",
      "open": 103700.0,
      "high": 103800.0,
      "low": 103650.0,
      "close": 103737.0,
      "volume": 56.8
    }
  ]
}
```

### Successful Response (After Aggregation)
```json
{
  "success": true,
  "sources_used": ["bitstamp", "kraken"],
  "sources_failed": [],
  "error_details": {},
  "metadata": {
    "aggregation_method": "equal_weight_averaging",
    "all_timestamps_count": 24,
    "total_sources": 2,
    "sources_excluded": ["coindesk"],
    "source_completeness": {
      "bitstamp": {
        "completeness_percentage": 100.0,
        "actual_candles": 24,
        "expected_candles": 24,
        "errors": []
      },
      "kraken": {
        "completeness_percentage": 100.0,
        "actual_candles": 24,
        "expected_candles": 24,
        "errors": []
      }
    },
    "aggregation_strategy": "100% completeness required",
    "total_sources_discovered": 3,
    "sources_with_complete_data": 2,
    "stored_in_aggregated_db": true,
    "aggregated_records_count": 24
  },
  "cache_hit": false,
  "refreshed_from_apis": true,
  "data": [...]
}
```

### Error Response
```json
{
  "error": "Failed to retrieve or aggregate OHLC data from any source",
  "success": false,
  "sources_failed": ["coindesk", "bitstamp", "kraken"],
  "error_details": {
    "cache_validation_errors": ["DataFrame is empty"],
    "source_aggregation_errors": {
      "coindesk": "Incomplete data: ['Missing timestamps: 5 gaps detected']"
    },
    "api_refresh_aggregation_errors": {
      "bitstamp": "API error: Rate limit exceeded"
    }
  },
  "cache_hit": false,
  "refreshed_from_apis": true,
  "metadata": {
    "timestamp": 1749653696,
    "requested": "btc-usd h1 24"
  }
}
```

## ⚙️ Configuration

Key configuration options in `app/config.py`:

```python
# ECC (Exclude Current Candle) Configuration
EXCLUDE_CURRENT_CANDLE_BITSTAMP = True

# Market Configuration for Sources
DEFAULT_MARKET_FOR_COINDESK = 'bitfinex'

# Logging
LOG_LEVEL = 'DEBUG'

# Parameter Validation Configuration
VALID_TIMEFRAMES = ['m1', 'm5', 'm15', 'h1', 'h4', 'd1']
DATE_FORMAT = '%d%m%Y'
MIN_CANDLES = 1
MAX_CANDLES = 50000
```

### Core Aggregation Settings (Hardcoded)
- **Aggregation Method**: Equal-weight averaging for OHLC data
- **Volume Aggregation**: Sum across all sources
- **Source Selection**: 100% completeness requirement (no partial data)
- **Validation Strategy**: Cache validation triggers refresh, final validation blocks response
- **Error Handling**: Continue with available sources, detailed error tracking

## 🧪 Testing

The project uses a clean test structure located in the `tests/` directory:

```
tests/
├── __init__.py
├── test_aggregation.py      # Data aggregation logic tests
├── test_endpoints.py        # Endpoint functionality tests
├── test_integration.py      # Full integration tests
└── source_tests/
    └── test_get_candles_from_source.py  # Individual source tests
```

### Run Tests
```bash
# Run all tests
python -m tests.test_integration

# Test individual components
python -m tests.test_aggregation
python -m tests.test_endpoints

# Test specific sources
python -m tests.source_tests.test_get_candles_from_source
```

### Expected Test Scenarios
- **Source Discovery**: Automatic detection of available source functions
- **Data Validation**: Cache vs final validation behavior
- **Aggregation Logic**: Equal-weight averaging with 100% completeness requirement
- **Error Handling**: Graceful degradation when sources fail
- **Cache Behavior**: Cache hits vs cache misses with refresh triggers
- **Response Structure**: Correct metadata and error reporting

## 🔧 Development

### Adding New Data Sources

1. **Create source directory**
```bash
mkdir app/sources/newsource
```

2. **Implement required functions**
```python
# app/sources/newsource/get_candles_from_newsource.py
def get_candles_from_newsource(currency_pair: str, timeframe: str, candles: int, ecc: bool = True):
    # Implementation here
    return dataframe

# app/sources/newsource/get_range_from_newsource.py  
def get_range_from_newsource(currency_pair: str, timeframe: str, from_date: str, to_date: str, candles: int, ecc: bool):
    # Implementation here
    return dataframe
```

3. **Sources are automatically discovered** - no additional configuration needed!

### Data Format Requirements

All source functions must return pandas DataFrames with these columns:
- `timestamp`: int64 (Unix timestamp)
- `date`: datetime64[ns, UTC] (timezone-aware)
- `open`, `high`, `low`, `close`, `volume`: float64

## 📈 Performance & Characteristics

### Response Times
- **Cache Hit (Step 1)**: ~50ms - Direct aggregated database access
- **Source Aggregation (Step 2)**: ~200-500ms - Aggregation from source databases
- **API Refresh + Aggregation (Steps 3-4)**: ~2-5 seconds - Full external API refresh
- **Concurrent Requests**: Supported via FastAPI async capabilities

### Database Performance
- **SQLite with Optimized Indexing**: Separate databases per currency pair
- **Aggregated Database**: Acts as high-performance cache layer
- **Source Databases**: Individual storage per source for data integrity

### Validation Performance
- **Cache Validation**: Fast structural checks, quality issues trigger refresh
- **Final Validation**: Comprehensive validation after aggregation attempts
- **100% Completeness Check**: Efficient source filtering before aggregation

## 🛡️ Error Handling & Resilience

The system implements comprehensive error handling with multiple fallback strategies:

### Source-Level Resilience
- **Individual Source Failures**: System continues with remaining sources
- **100% Completeness Requirement**: Incomplete sources are excluded, not used partially
- **Detailed Error Tracking**: Per-source error details in response metadata
- **API Rate Limits**: Graceful handling with specific error messages

### Validation Strategy
- **Cache Validation**: Data quality issues trigger refresh (non-blocking)
  - Stale data, wrong candle count, missing timestamps
- **Final Validation**: Structural issues block response (blocking)
  - Empty DataFrames, invalid data types, persistent completeness failures

### Multi-Step Fallback Process
1. **Step 1**: Try aggregated database (cache)
2. **Step 2**: Try aggregation from source databases
3. **Step 3**: Refresh source databases from APIs
4. **Step 4**: Re-attempt aggregation with fresh data
5. **Final**: Return detailed error if all steps fail

### Error Response Structure
- **Comprehensive Error Details**: Separate error tracking for each step
- **Source Completeness Info**: Detailed metadata about why sources were excluded
- **Timestamp Tracking**: When errors occurred and what was requested
- **Graceful Degradation**: Always attempts to return best available data

## 🔮 Future Enhancements

- **Async Processing**: Concurrent API calls for improved performance
- **Advanced Validation**: More sophisticated data quality and anomaly detection
- **Weighted Averaging**: Source-specific reliability weights instead of equal weighting
- **Real-time Updates**: WebSocket support for live data streaming
- **Advanced Caching**: Redis integration for distributed caching across instances
- **Monitoring & Metrics**: Comprehensive observability with source health tracking
- **Smart Source Selection**: Dynamic source prioritization based on historical reliability
- **Data Quality Scoring**: Automated quality assessment and source ranking

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

For support, please open an issue in the GitHub repository or contact the development team.

---

**Built with ❤️ for the cryptocurrency community**
