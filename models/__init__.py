"""
Model providers package.
"""
from typing import Dict, Any

from config.config import Config
from models.base import ModelProvider
from models.llamacpp_provider import LlamaCppProvider

def get_model_provider(provider_name: str) -> LlamaCppProvider:
    """
    Factory function to get a model provider based on the provider name.

    Args:
        provider_name: The name of the provider (e.g., "openai", "anthropic")

    Returns:
        A ModelProvider instance

    Raises:
        ValueError: If the provider is not supported
    """
    config = Config()
    model_config = config.get_model_config()

    if provider_name.lower() == "llamacpp":
        return LlamaCppProvider(model_config, api_url=config.llamacpp_api_url)
    else:
        raise ValueError(f"Unsupported model provider: {provider_name}")

__all__ = ["ModelProvider", "LlamaCppProvider", "get_model_provider"]
