"""
Core ReAct Agent Implementation

Implements the ReAct (Reasoning + Acting) pattern:
User Input → Agent + Memory → Reasoning → Action → Tool Execution → 
Observation → Memory Update → Repeat until Final Answer
"""
import re
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

from logger.get_logger import log


class ActionType(Enum):
    """Types of actions the agent can take."""
    TOOL_CALL = "tool_call"
    FINAL_ANSWER = "final_answer"


@dataclass
class ReActStep:
    """Represents a single step in the ReAct reasoning process."""
    thought: str
    action_type: ActionType
    action: Optional[str] = None
    action_input: Optional[str] = None
    observation: Optional[str] = None
    step_number: int = 0


class ReActAgent:
    """
    Core ReAct Agent following the specified flow pattern.
    
    Modular design allows easy extension of:
    - Tools (via tools interface)
    - Memory (via memory interface) 
    - LLM providers (via models interface)
    """
    
    def __init__(self, llm, tools: Dict[str, Any], memory_manager, max_iterations: int = 5):
        """
        Initialize ReAct Agent.
        
        Args:
            llm: Language model instance
            tools: Dictionary of available tools {name: tool_object}
            memory_manager: Memory management instance
            max_iterations: Maximum reasoning iterations
        """
        self.llm = llm
        self.tools = tools
        self.memory_manager = memory_manager
        self.max_iterations = max_iterations
        
        # Create tool descriptions for prompts
        self.tool_descriptions = self._create_tool_descriptions()
        
    def _create_tool_descriptions(self) -> str:
        """Create formatted tool descriptions for the prompt."""
        descriptions = []
        for name, tool in self.tools.items():
            if hasattr(tool, 'description'):
                descriptions.append(f"- {name}: {tool.description}")
            else:
                descriptions.append(f"- {name}: Available tool")
        return "\n".join(descriptions)
    
    def _classify_user_intent(self, user_input: str) -> str:
        """Classify user intent to determine if tools are needed."""

        question_lower = user_input.lower()

        # Tool-requiring intents
        if any(keyword in question_lower for keyword in ["date", "today", "when is"]):
            return "date_query"
        elif any(keyword in question_lower for keyword in ["time", "clock", "what time"]):
            return "time_query"
        elif any(op in user_input for op in ["+", "-", "*", "/", "calculate", "="]):
            return "math_query"
        elif any(keyword in question_lower for keyword in ["weather", "temperature", "rain", "sunny"]):
            return "weather_query"
        elif any(keyword in question_lower for keyword in ["search", "find", "look up", "google"]):
            return "search_query"

        # Conversational intents (no tools needed)
        elif any(greeting in question_lower for greeting in ["hello", "hi", "hey", "how are you", "good morning", "good afternoon", "good evening"]):
            return "greeting"
        elif any(keyword in question_lower for keyword in ["thank", "thanks", "appreciate"]):
            return "gratitude"
        elif any(keyword in question_lower for keyword in ["help", "what can you do", "capabilities", "what do you do"]):
            return "help_request"
        elif len(user_input.split()) < 8 and "?" not in user_input and not any(op in user_input for op in ["+", "-", "*", "/"]):
            return "casual_chat"

        # Default: try to determine if it needs tools
        return "unknown"

    def _build_chat_prompt(self, user_input: str) -> str:
        """Build a simple chat prompt for conversational queries."""

        user_lower = user_input.lower()

        if any(greeting in user_lower for greeting in ["hello", "hi", "hey", "how are you"]):
            return f"""You are a friendly AI assistant. Respond warmly to the user's greeting.

User: {user_input}
Assistant:"""
        elif any(word in user_lower for word in ["thank", "thanks"]):
            return f"""You are a helpful AI assistant. Respond politely to the user's thanks.

User: {user_input}
Assistant:"""
        else:
            return f"""You are a knowledgeable AI assistant. Answer the user's question directly and helpfully.

User: {user_input}
Assistant:"""

    def _handle_conversational_query(self, user_input: str, iteration: int) -> ReActStep:
        """Handle conversational queries directly with LLM."""

        print(f"💬 Handling as conversational query: '{user_input}'")

        # Use simple chat prompt
        chat_prompt = self._build_chat_prompt(user_input)

        # Get LLM response
        llm_response = self.llm.invoke(chat_prompt)

        # Extract response text
        if hasattr(llm_response, 'content'):
            response_text = llm_response.content.strip()
        elif hasattr(llm_response, 'text'):
            response_text = llm_response.text.strip()
        else:
            response_text = str(llm_response).strip()

        # Clean up response
        if response_text.startswith("Assistant:"):
            response_text = response_text[10:].strip()

        # Handle empty responses
        if not response_text or len(response_text.strip()) < 3:
            response_text = "I understand. Is there anything else I can help you with?"

        print(f"💬 Chat response: '{response_text}'")

        # Return as final answer
        return ReActStep(
            thought="Providing conversational response",
            action_type=ActionType.FINAL_ANSWER,
            action=response_text,
            step_number=iteration
        )

    @log
    def process(self, user_input: str) -> str:
        """
        Main processing method implementing the ReAct flow.
        
        Flow:
        1. Load memory context
        2. Iterative reasoning loop:
           - Agent reasoning with context
           - Action decision (tool call or final answer)
           - Tool execution and observation (if tool call)
           - Memory update with reasoning step
        3. Save final conversation to memory
        
        Args:
            user_input: User's input/question
            
        Returns:
            Final answer from the agent
        """
        print(f"\n🤖 ReAct Agent Processing: {user_input}")
        print("=" * 60)

        # Step 1: Classify user intent
        intent = self._classify_user_intent(user_input)
        print(f"🎯 Detected intent: {intent}")

        # Step 2: Route based on intent
        if intent in ["greeting", "gratitude", "help_request", "casual_chat"]:
            # Handle conversationally
            print("💬 Routing to conversational handler")
            step = self._handle_conversational_query(user_input, 1)
            self._save_conversation_to_memory(user_input, step.action, [step])
            return step.action

        elif intent == "unknown":
            # Try conversational first, then fall back to tools if needed
            print("❓ Routing to unknown query handler")
            return self._handle_unknown_query(user_input)

        # Step 3: Load memory context for tool-based queries
        memory_context = self._load_memory_context(user_input)

        # Step 4: Initialize reasoning process
        reasoning_steps = []
        iteration = 0
        
        while iteration < self.max_iterations:
            iteration += 1
            print(f"\n🔄 Iteration {iteration}/{self.max_iterations}")
            print("-" * 30)
            
            # Step 3: Agent reasoning with current context
            if not reasoning_steps:
                # First iteration: use rule-based tool selection
                step = self._rule_based_tool_selection(user_input, iteration)
            else:
                # Subsequent iterations: use LLM for final answer
                step = self._agent_reasoning(user_input, memory_context, reasoning_steps, iteration)
            reasoning_steps.append(step)
            
            print(f"💭 Thought: {step.thought}")
            
            # Step 4: Action decision and execution
            if step.action_type == ActionType.TOOL_CALL:
                print(f"🔧 Action: {step.action}")
                print(f"📝 Input: {step.action_input}")
                
                # Execute tool and observe
                observation = self._execute_tool(step.action, step.action_input)
                step.observation = observation
                
                print(f"👁️ Observation: {observation}")
                
                # Update memory with reasoning step
                self._update_memory_with_step(memory_context, step)

                # For rule-based approach, create final answer directly
                if step.observation:
                    final_answer = self._create_final_answer(user_input, step.observation)
                    self._save_conversation_to_memory(user_input, final_answer, reasoning_steps)
                    return final_answer
                
            elif step.action_type == ActionType.FINAL_ANSWER:
                print(f"✅ Final Answer: {step.action}")
                
                # Save complete conversation to memory and return
                self._save_conversation_to_memory(user_input, step.action, reasoning_steps)
                return step.action
        
        # If max iterations reached without final answer
        final_answer = "I've reached the maximum number of reasoning steps. Based on my analysis, I'll provide the best answer I can."
        self._save_conversation_to_memory(user_input, final_answer, reasoning_steps)
        return final_answer
    
    def _load_memory_context(self, user_input: str) -> str:
        """Load relevant memory context for the current input."""
        print("🧠 Loading memory context...")
        
        memory_vars = self.memory_manager.load_memory_variables({"input": user_input})
        return memory_vars.get("chat_history", "")
    
    def _agent_reasoning(self, user_input: str, memory_context: str,
                        reasoning_steps: List[ReActStep], iteration: int) -> ReActStep:
        """
        Core agent reasoning step.

        Args:
            user_input: Original user input
            memory_context: Current memory context
            reasoning_steps: Previous reasoning steps
            iteration: Current iteration number

        Returns:
            ReActStep with agent's reasoning and action decision
        """
        # Build context for LLM
        context = self._build_reasoning_context(user_input, memory_context, reasoning_steps)

        # If context is None, it means we should use rule-based selection
        if context is None:
            return self._rule_based_tool_selection(user_input, iteration)

        # Get LLM response
        llm_response = self.llm.invoke(context)

        # Extract text content from response
        if hasattr(llm_response, 'content'):
            response_text = llm_response.content
        elif hasattr(llm_response, 'text'):
            response_text = llm_response.text
        else:
            response_text = str(llm_response)

        # Validate response length to prevent runaway generation
        if len(response_text) > 1000:
            print(f"⚠️ LLM response too long ({len(response_text)} chars), truncating...")
            response_text = response_text[:1000]

        # Parse LLM response into ReAct step
        return self._parse_llm_response(response_text, iteration)
    
    def _build_reasoning_context(self, user_input: str, memory_context: str,
                                reasoning_steps: List[ReActStep]) -> str:
        """Build a simple, direct prompt optimized for 8B models."""

        # Skip LLM for tool selection - use rule-based approach
        if not reasoning_steps:
            # Don't use LLM for tool selection, handle it directly
            return None  # Signal to use rule-based tool selection

        # For subsequent iterations, use minimal context for final answer
        return self._build_followup_prompt(user_input, reasoning_steps[-1])

    def _build_direct_tool_prompt(self, user_input: str) -> str:
        """Direct tool selection without complex reasoning."""

        question_lower = user_input.lower()

        if "date" in question_lower or "today" in question_lower:
            prompt = f"""You must use the get_current_date tool to answer this question.

Question: {user_input}

Complete this response:
Action: get_current_date
Input:"""
            print(f"🔍 Date prompt: {repr(prompt)}")
            return prompt

        elif "time" in question_lower:
            prompt = f"""You must use the get_current_time tool to answer this question.

Question: {user_input}

Complete this response:
Action: get_current_time
Input:"""
            print(f"🔍 Time prompt: {repr(prompt)}")
            return prompt

        elif any(op in user_input for op in ["+", "-", "*", "/", "calculate", "="]):
            math_expr = self._extract_math_expression(user_input)
            prompt = f"""You must use the math_calculator tool to answer this question.

Question: {user_input}

Complete this response:
Action: math_calculator
Input: {math_expr}"""
            print(f"🔍 Math prompt: {repr(prompt)}")
            return prompt

        else:
            prompt = f"""You must use the search_web tool to answer this question.

Question: {user_input}

Complete this response:
Action: search_web
Input: {user_input}"""
            print(f"🔍 Default prompt: {repr(prompt)}")
            return prompt

    def _extract_math_expression(self, text: str) -> str:
        """Extract mathematical expression from text."""
        # Remove common words
        text = text.replace("Calculate", "").replace("calculate", "")
        text = text.replace("What is", "").replace("what is", "")
        text = text.strip()
        return text if any(op in text for op in ["+", "-", "*", "/"]) else text

    def _build_followup_prompt(self, user_input: str, last_step: ReActStep) -> str:
        """Build prompt for final answer after tool execution."""

        if last_step.observation:
            # Create a user-friendly final answer based on the question type
            question_lower = user_input.lower()
            result = last_step.observation

            if "date" in question_lower or "today" in question_lower:
                return f"""Question: {user_input}
Tool result: {result}

Provide a friendly response about today's date:
Action: Final Answer
Input: Today's date is {result}."""

            elif "time" in question_lower:
                return f"""Question: {user_input}
Tool result: {result}

Provide a friendly response about the current time:
Action: Final Answer
Input: The current time is {result}."""

            elif any(op in user_input for op in ["+", "-", "*", "/", "calculate", "="]):
                return f"""Question: {user_input}
Tool result: {result}

Provide a friendly response with the calculation result:
Action: Final Answer
Input: The answer is {result}."""

            else:
                return f"""Question: {user_input}
Tool result: {result}

Provide a helpful response:
Action: Final Answer
Input: {result}"""

        return f"""Question: {user_input}
Action: Final Answer
Input: I cannot answer this question."""

    def _rule_based_tool_selection(self, user_input: str, iteration: int) -> ReActStep:
        """Rule-based tool selection without LLM reasoning."""

        print(f"🎯 Rule-based tool selection for: '{user_input}'")

        question_lower = user_input.lower()

        if "date" in question_lower or "today" in question_lower:
            print("📅 Selected tool: get_current_date")
            return ReActStep(
                thought="User wants current date - using get_current_date tool",
                action_type=ActionType.TOOL_CALL,
                action="get_current_date",
                action_input="",
                step_number=iteration
            )

        elif "time" in question_lower:
            print("🕐 Selected tool: get_current_time")
            return ReActStep(
                thought="User wants current time - using get_current_time tool",
                action_type=ActionType.TOOL_CALL,
                action="get_current_time",
                action_input="",
                step_number=iteration
            )

        elif any(op in user_input for op in ["+", "-", "*", "/", "calculate", "="]):
            math_expr = self._extract_math_expression(user_input)
            print(f"🧮 Selected tool: math_calculator with expression: {math_expr}")
            return ReActStep(
                thought=f"User wants calculation - using math_calculator tool",
                action_type=ActionType.TOOL_CALL,
                action="math_calculator",
                action_input=math_expr,
                step_number=iteration
            )

        elif "weather" in question_lower:
            location = user_input.replace("weather", "").replace("What's the", "").replace("in", "").strip()
            print(f"🌤️ Selected tool: get_weather for location: {location}")
            return ReActStep(
                thought="User wants weather information - using get_weather tool",
                action_type=ActionType.TOOL_CALL,
                action="get_weather",
                action_input=location,
                step_number=iteration
            )

        else:
            # Check if it's a knowledge question that should use search
            if any(word in question_lower for word in ["what is", "who is", "where is", "capital", "country", "president"]):
                print("🔍 Selected tool: search_web (knowledge query)")
                return ReActStep(
                    thought="User asking for factual information - using search tool",
                    action_type=ActionType.TOOL_CALL,
                    action="search_web",
                    action_input=user_input,
                    step_number=iteration
                )
            else:
                print("🔍 Selected tool: search_web (default)")
                return ReActStep(
                    thought="Using search tool for general query",
                    action_type=ActionType.TOOL_CALL,
                    action="search_web",
                    action_input=user_input,
                    step_number=iteration
                )
    

    def _parse_llm_response(self, response: str, iteration: int) -> ReActStep:
        """Simple parsing optimized for direct responses."""

        print(f"🔍 Parsing response: {repr(response[:100])}...")

        response = response.strip()

        # Check if response contains Action: format
        if "Action:" in response:
            lines = response.split('\n')
            action_line = next((line for line in lines if line.startswith("Action:")), "")
            input_line = next((line for line in lines if line.startswith("Input:")), "")

            action = action_line.replace("Action:", "").strip()
            action_input = input_line.replace("Input:", "").strip()

            print(f"📝 Parsed - Action: '{action}', Input: '{action_input}'")

            if action.lower() == "final answer":
                return ReActStep(
                    thought="Providing final answer",
                    action_type=ActionType.FINAL_ANSWER,
                    action=action_input or response,
                    step_number=iteration
                )
            else:
                return ReActStep(
                    thought=f"Using tool: {action}",
                    action_type=ActionType.TOOL_CALL,
                    action=action,
                    action_input=action_input,
                    step_number=iteration
                )

        # Check if response looks like a tool completion (just the input part)
        elif len(response) < 50 and not any(word in response.lower() for word in ["i", "the", "today", "answer"]):
            # This might be just the Input: part of the response
            # Try to infer the tool from the current context
            print(f"📝 Inferring tool from short response: '{response}'")

            # This is a fallback - we'll treat it as a final answer for now
            return ReActStep(
                thought="Short response - treating as final answer",
                action_type=ActionType.FINAL_ANSWER,
                action=response,
                step_number=iteration
            )

        # Fallback: treat entire response as final answer
        print(f"📝 Fallback - treating as final answer: '{response[:50]}...'")
        return ReActStep(
            thought="Direct response",
            action_type=ActionType.FINAL_ANSWER,
            action=response,
            step_number=iteration
        )
    
    def _execute_tool(self, tool_name: str, tool_input: str) -> str:
        """Execute the specified MCP tool with given input."""

        if tool_name not in self.tools:
            return f"Error: Tool '{tool_name}' not found. Available tools: {list(self.tools.keys())}"

        try:
            tool = self.tools[tool_name]
            # Handle MCP tool wrapper
            if hasattr(tool, 'func'):
                result = tool.func(tool_input)
            elif callable(tool):
                result = tool(tool_input)
            else:
                result = str(tool)

            return str(result)

        except Exception as e:
            return f"Error executing MCP tool '{tool_name}': {str(e)}"
    
    def _update_memory_with_step(self, memory_context: str, step: ReActStep):
        """Update memory with the current reasoning step."""
        # In this implementation, we update context in the final save
        # This method can be extended for more sophisticated memory updates
        pass
    
    def _save_conversation_to_memory(self, user_input: str, final_answer: str, 
                                   reasoning_steps: List[ReActStep]):
        """Save the complete conversation to memory."""
        
        # Create reasoning summary
        reasoning_summary = self._create_reasoning_summary(reasoning_steps)
        
        # Save to memory manager
        self.memory_manager.save_context(
            {"input": user_input},
            {"output": final_answer, "reasoning": reasoning_summary}
        )
        
        print(f"\n💾 Saved conversation to memory")
        print(f"📊 Reasoning steps: {len(reasoning_steps)}")
    
    def _create_reasoning_summary(self, steps: List[ReActStep]) -> str:
        """Create a summary of the reasoning process."""
        if not steps:
            return "Direct answer without tool usage"
        
        summary_parts = []
        for step in steps:
            if step.action_type == ActionType.TOOL_CALL:
                summary_parts.append(f"Used {step.action} tool")
            
        return f"Reasoning process: {' → '.join(summary_parts)}" if summary_parts else "Direct reasoning"

    def _create_final_answer(self, user_input: str, tool_result: str) -> str:
        """Create a user-friendly final answer based on the question type and tool result."""

        question_lower = user_input.lower()

        if "date" in question_lower or "today" in question_lower:
            return f"Today's date is {tool_result}."

        elif "time" in question_lower:
            return f"The current time is {tool_result}."

        elif any(op in user_input for op in ["+", "-", "*", "/", "calculate", "="]):
            return f"The answer is {tool_result}."

        elif "weather" in question_lower:
            return tool_result  # Weather tool already provides formatted response

        else:
            return tool_result

    def _handle_unknown_query(self, user_input: str) -> str:
        """Handle queries with unknown intent - try chat first, then tools if needed."""

        print(f"❓ Handling unknown query: '{user_input}'")

        # First, try conversational response
        try:
            step = self._handle_conversational_query(user_input, 1)
            response = step.action

            # Check if the response seems like it's trying to use tools or is unsatisfactory
            response_lower = response.lower()
            if any(phrase in response_lower for phrase in [
                "i don't know", "i cannot", "i can't", "i'm not sure",
                "i need to", "let me search", "i would need to",
                "i understand. is there anything else", "i understand, is there anything else"
            ]) or len(response.strip()) < 10:
                print("🔄 Chat response unsatisfactory, trying tools...")
                # Fall back to tool-based approach
                return self._handle_with_tools(user_input)
            else:
                print("✅ Chat response satisfactory")
                self._save_conversation_to_memory(user_input, response, [step])
                return response

        except Exception as e:
            print(f"❌ Chat failed: {e}, falling back to tools")
            return self._handle_with_tools(user_input)

    def _handle_with_tools(self, user_input: str) -> str:
        """Handle query using the existing tool-based approach."""

        print("🔧 Using tool-based approach")

        # Load memory context
        memory_context = self._load_memory_context(user_input)

        # Initialize reasoning process
        reasoning_steps = []
        iteration = 0

        while iteration < self.max_iterations:
            iteration += 1
            print(f"\n🔄 Iteration {iteration}/{self.max_iterations}")
            print("-" * 30)

            # Use rule-based tool selection for first iteration
            if not reasoning_steps:
                step = self._rule_based_tool_selection(user_input, iteration)
            else:
                step = self._agent_reasoning(user_input, memory_context, reasoning_steps, iteration)

            reasoning_steps.append(step)
            print(f"💭 Thought: {step.thought}")

            # Execute tool and create final answer
            if step.action_type == ActionType.TOOL_CALL:
                print(f"🔧 Action: {step.action}")
                print(f"📝 Input: {step.action_input}")

                observation = self._execute_tool(step.action, step.action_input)
                step.observation = observation
                print(f"👁️ Observation: {observation}")

                # Create final answer directly
                if step.observation:
                    final_answer = self._create_final_answer(user_input, step.observation)
                    self._save_conversation_to_memory(user_input, final_answer, reasoning_steps)
                    return final_answer

            elif step.action_type == ActionType.FINAL_ANSWER:
                print(f"✅ Final Answer: {step.action}")
                self._save_conversation_to_memory(user_input, step.action, reasoning_steps)
                return step.action

        # If max iterations reached
        final_answer = "I've tried to help but couldn't find a satisfactory answer."
        self._save_conversation_to_memory(user_input, final_answer, reasoning_steps)
        return final_answer
