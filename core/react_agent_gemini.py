"""
Core ReAct Agent Implementation - Refactored for CPU-based LLMs

Implements the ReAct (Reasoning + Acting) pattern:
User Input → Agent + Memory → Reasoning → Action → Tool Execution →
Observation → Memory Update → Repeat until Final Answer
"""
import re
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

# A simple logger for demonstration purposes.
# You can replace this with your actual logger.
from logging import getLogger, basicConfig, INFO

basicConfig(level = INFO)
log = getLogger(__name__)


class ActionType(Enum):
    """Types of actions the agent can take."""
    TOOL_CALL = "tool_call"
    FINAL_ANSWER = "final_answer"


@dataclass
class ReActStep:
    """Represents a single step in the ReAct reasoning process."""
    thought: str
    action_type: ActionType
    action: Optional[str] = None
    action_input: Optional[str] = None
    observation: Optional[str] = None
    step_number: int = 0


class ReActAgent:
    """
    Core ReAct Agent optimized for smaller, CPU-based language models.

    This refactored design emphasizes:
    - Clear and concise system prompts.
    - Letting the LLM drive the reasoning process.
    - Unified logic for handling both tool-based and conversational queries.
    - Robust parsing and error handling.
    """

    def __init__(self, llm, tools: Dict[str, Any], memory_manager, max_iterations: int = 5):
        """
        Initialize ReAct Agent.

        Args:
            llm: Language model instance with an `invoke` method.
            tools: Dictionary of available tools {name: tool_object}.
            memory_manager: Memory management instance.
            max_iterations: Maximum reasoning iterations.
        """
        self.llm = llm
        self.tools = tools
        self.memory_manager = memory_manager
        self.max_iterations = max_iterations
        self.tool_descriptions = self._create_tool_descriptions()
        self.system_prompt = self._build_system_prompt()

    def _create_tool_descriptions(self) -> str:
        """Create formatted tool descriptions for the prompt."""
        descriptions = []
        for name, tool in self.tools.items():
            # Assumes tools have a .description attribute
            description = getattr(tool, 'description', 'No description available.')
            descriptions.append(f"- {name}: {description}")
        return "\n".join(descriptions)

    def _build_system_prompt(self) -> str:
        """Builds a system prompt that instructs the LLM on its role and expected output format."""
        return f"""You are a helpful and conversational assistant. Your goal is to answer the user's question.
You have access to the following tools to help you:
{self.tool_descriptions}

To use a tool, you MUST respond in the following format, and only this format:
Thought: [Your reasoning on which tool to use and why]
Action: [The name of the tool to use]
Input: [The input for the tool]

If you believe you have the final answer, or if the user is just making conversation,
you MUST respond in the following format:
Thought: [Your reasoning for providing the final answer]
Action: Final Answer
Input: [Your final, conversational answer to the user]

Begin!"""

    def process(self, user_input: str) -> str:
        """
        Main processing method implementing the ReAct flow.
        This method now serves as the single entry point for all user queries.
        """
        log.info(f"🤖 ReAct Agent Processing: '{user_input}'")

        # Load chat history from memory
        memory_context = self.memory_manager.load_memory_variables({"input": user_input}).get("chat_history", "")
        reasoning_steps: List[ReActStep] = []

        for i in range(self.max_iterations):
            iteration = i + 1
            log.info(f"🔄 Iteration {iteration}/{self.max_iterations}")

            # Build the prompt for the current turn
            current_prompt = self._build_iteration_prompt(memory_context, user_input, reasoning_steps)

            # Get LLM response
            llm_response = self.llm.invoke(current_prompt)
            response_text = getattr(llm_response, 'content', str(llm_response)).strip()

            # Parse the LLM's response to decide the next step
            step = self._parse_llm_response(response_text, iteration)
            reasoning_steps.append(step)

            log.info(f"💭 Thought: {step.thought}")

            if step.action_type == ActionType.FINAL_ANSWER:
                log.info(f"✅ Final Answer: {step.action}")
                self._save_conversation_to_memory(user_input, step.action, reasoning_steps)
                return step.action

            if step.action_type == ActionType.TOOL_CALL:
                log.info(f"🔧 Action: {step.action}")
                log.info(f"📝 Input: {step.action_input}")

                observation = self._execute_tool(step.action, step.action_input)
                step.observation = observation
                log.info(f"👁️ Observation: {observation}")
                # The observation will be included in the prompt for the next iteration

        # If max iterations are reached
        final_answer = "I seem to be having trouble finding the answer. Could you please rephrase the question?"
        self._save_conversation_to_memory(user_input, final_answer, reasoning_steps)
        return final_answer

    def _build_iteration_prompt(self, memory_context: str, user_input: str, steps: List[ReActStep]) -> str:
        """Builds the prompt for each iteration of the reasoning loop."""
        # Combine previous turns into a coherent history
        reasoning_history = []
        for step in steps:
            reasoning_history.append(f"Thought: {step.thought}")
            if step.action_type == ActionType.TOOL_CALL:
                reasoning_history.append(f"Action: {step.action}")
                reasoning_history.append(f"Input: {step.action_input}")
            if step.observation:
                reasoning_history.append(f"Observation: {step.observation}")

        # The history from memory manager should come first
        full_history = f"{memory_context}\n{''.join(reasoning_history)}"

        return f"{self.system_prompt}\n\n{full_history}\nQuestion: {user_input}\n"

    def _parse_llm_response(self, response: str, iteration: int) -> ReActStep:
        """
        Parses the LLM's raw output to determine the thought, action, and input.
        This parser is more resilient to variations in model output.
        """
        log.info(f"🔍 Parsing LLM Response:\n---\n{response}\n---")

        thought_match = re.search(r"Thought:\s*(.*)", response, re.DOTALL)
        action_match = re.search(r"Action:\s*(\S+)", response, re.DOTALL)  # Action is a single word
        input_match = re.search(r"Input:\s*(.*)", response, re.DOTALL)

        thought = thought_match.group(1).strip() if thought_match else "No thought provided."

        if not action_match:
            # If no "Action:" keyword is found, treat the entire response as a final answer.
            # This makes the agent robust to conversational replies from the LLM.
            return ReActStep(
                    thought = "The model decided to answer directly without using a tool.",
                    action_type = ActionType.FINAL_ANSWER,
                    action = response,
                    step_number = iteration
            )

        action = action_match.group(1).strip()
        action_input = input_match.group(1).strip() if input_match else ""

        if "final" in action.lower() and "answer" in action.lower():
            return ReActStep(
                    thought = thought,
                    action_type = ActionType.FINAL_ANSWER,
                    action = action_input,
                    step_number = iteration
            )
        else:
            return ReActStep(
                    thought = thought,
                    action_type = ActionType.TOOL_CALL,
                    action = action,
                    action_input = action_input,
                    step_number = iteration
            )

    def _execute_tool(self, tool_name: str, tool_input: str) -> str:
        """Execute the specified MCP tool with given input."""

        if tool_name not in self.tools:
            return f"Error: Tool '{tool_name}' not found. Available tools: {list(self.tools.keys())}"

        try:
            tool = self.tools[tool_name]
            # Handle MCP tool wrapper (same as original agent)
            if hasattr(tool, 'func'):
                result = tool.func(tool_input)
            elif callable(tool):
                result = tool(tool_input)
            else:
                result = str(tool)

            return str(result)

        except Exception as e:
            log.error(f"Error executing tool '{tool_name}': {e}")
            return f"Error executing MCP tool '{tool_name}': {str(e)}"

    def _save_conversation_to_memory(self, user_input: str, final_answer: str,
                                     reasoning_steps: List[ReActStep]):
        """Saves the complete conversation history to the memory manager."""
        reasoning_summary = self._create_reasoning_summary(reasoning_steps)
        self.memory_manager.save_context(
                {"input": user_input},
                {"output": final_answer, "reasoning": reasoning_summary}
        )
        log.info(f"💾 Conversation saved to memory.")
        log.info(f"📊 Reasoning summary: {reasoning_summary}")

    def _create_reasoning_summary(self, steps: List[ReActStep]) -> str:
        """Creates a concise summary of the reasoning process for logging."""
        if not steps:
            return "Direct answer without tool usage."

        summary_parts = []
        for step in steps:
            if step.action_type == ActionType.TOOL_CALL:
                # Truncate observation for cleaner logs
                obs_preview = str(step.observation).replace('\n', ' ').strip()
                obs_preview = (obs_preview[:70] + '...') if len(obs_preview) > 70 else obs_preview
                summary_parts.append(f"Used {step.action}('{step.action_input}') → Got '{obs_preview}'")

        if not summary_parts:
            return "Conversational answer."

        return " → ".join(summary_parts)
