#!/usr/bin/env python3
"""
Simple connectivity test for the LLaMA.cpp server
"""
import requests
import json
import time

def test_connection():
    """Test connection to the LLaMA.cpp server."""
    
    url = "http://172.16.0.111:11111/completion"
    
    print("🔗 TESTING LLAMACPP SERVER CONNECTION")
    print("=" * 50)
    print(f"Server URL: {url}")
    
    # Simple test payload
    test_payload = {
        "prompt": "Hello, how are you?",
        "n_predict": 20,
        "temperature": 0.7,
        "stop": ["\n"]
    }
    
    try:
        print("📡 Sending test request...")
        start_time = time.time()
        
        response = requests.post(
            url, 
            headers={"Content-Type": "application/json"},
            data=json.dumps(test_payload),
            timeout=30
        )
        
        response_time = time.time() - start_time
        
        print(f"✅ Connection successful!")
        print(f"⏱️ Response time: {response_time:.2f}s")
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"📝 Response keys: {list(response_data.keys())}")
            
            # Extract content
            content = ""
            if "content" in response_data:
                content = response_data["content"]
            elif "response" in response_data:
                content = response_data["response"]
            elif "text" in response_data:
                content = response_data["text"]
            elif "choices" in response_data and len(response_data["choices"]) > 0:
                content = response_data["choices"][0].get("text", "")
            
            print(f"🤖 Model response: '{content}'")
            print(f"📏 Response length: {len(content)} characters")
            
            # Test model info if available
            try:
                info_response = requests.get("http://172.16.0.111:11111/props")
                if info_response.status_code == 200:
                    info_data = info_response.json()
                    print(f"🔍 Model info available: {list(info_data.keys())}")
            except:
                print("ℹ️ Model info endpoint not available")
            
            return True
        else:
            print(f"❌ Server returned error: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed: Server is not running or not accessible")
        print("💡 Please start the LLaMA.cpp service on the remote machine")
        return False
    except requests.exceptions.Timeout:
        print("❌ Connection timeout: Server is too slow to respond")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def wait_for_server(max_wait_time=300, check_interval=10):
    """Wait for the server to come online."""
    
    print(f"⏳ Waiting for server to come online (max {max_wait_time}s)...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait_time:
        if test_connection():
            print("🎉 Server is now online!")
            return True
        
        print(f"⏱️ Waiting {check_interval}s before next check...")
        time.sleep(check_interval)
    
    print("⏰ Timeout: Server did not come online within the specified time")
    return False

if __name__ == "__main__":
    if not test_connection():
        print("\n" + "="*50)
        print("🔧 TROUBLESHOOTING STEPS:")
        print("="*50)
        print("1. Check if the service is running on the remote machine:")
        print("   sudo systemctl status llamacpp-server")
        print("\n2. Start the service if it's not running:")
        print("   sudo systemctl start llamacpp-server")
        print("\n3. Check service logs:")
        print("   sudo journalctl -u llamacpp-server -f")
        print("\n4. Verify the container is running:")
        print("   podman ps | grep llamacpp")
        print("\n5. Check if port 11111 is open:")
        print("   netstat -tlnp | grep 11111")
        print("\n6. Test from the remote machine locally:")
        print("   curl -X POST http://localhost:11111/completion -H 'Content-Type: application/json' -d '{\"prompt\":\"test\",\"n_predict\":5}'")
