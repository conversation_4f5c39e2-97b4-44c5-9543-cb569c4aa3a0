import os
import pandas as pd
from sqlalchemy import create_engine, text

from app.db.helpers.create_db import create_db
from app.db.helpers.db_exists import db_exists
from app.logger.get_logger import logger
from app.logger.get_logger import log


@log
def get_from_db(db_name: str, table_name: str, klines: str = "1000", source: str = "aggregated"):
    # TODO: instead of exchange, it should take source parameter aggregated/bitstamp/kraken/coindesk/etc
    """
        Retrieves data from a SQLite database.

        Args:
            db_name (str): Database name. Example: "btc-usd"
            table_name (str): Table name. Example: h1
            klines (str, optional): Number of rows to retrieve. Defaults to "1000".
            source (str, optional): The name of the app/db/data/{source} (used as subfolder). Defaults to "aggregated".

        Returns:
            pd.DataFrame: Retrieved data.

        Raises:
            ValueError: If the specified table does not exist in the database.
            Exception: Any other unexpected error.
        """

    try:
        # Construct database path
        db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', source, db_name)

        # Check if database file exists
        if not db_exists(db_path):
            create_db(db_path)
            return None

        # Establish database connection
        engine = create_engine(f'sqlite:///{db_path}.db')

        # Check if table exists
        with engine.connect() as connection:
            result = connection.execute(
                    text(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'"))
            if not result.fetchone():
                raise ValueError(f"Table '{table_name}' in '{db_name}' does not exist in the database.")

        # Execute SQL query
        df = pd.read_sql_query(f"SELECT * FROM {table_name} ORDER BY timestamp DESC LIMIT {klines}",
                               engine, parse_dates=['date'])

        return df

    except ValueError as e:
        logger.error(f"Error in {os.path.basename(__file__)}: {e}")
        raise

    except Exception as e:
        logger.error(f"Error in {os.path.basename(__file__)}: {e}")
        raise
