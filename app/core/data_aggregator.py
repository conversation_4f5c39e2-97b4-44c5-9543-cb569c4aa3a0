import pandas as pd
from typing import List, Dict, Optional, Tuple
from app.logger.get_logger import log, logger
from app.core.aggregation_result import AggregationResult


class DataAggregator:
    """
    Handles aggregation of OHLC data from multiple sources.
    
    Uses equal-weight averaging for OHLC data and volume summing as per requirements.
    """
    
    @staticmethod
    @log
    def aggregate_ohlc_data(source_dataframes: Dict[str, pd.DataFrame]) -> AggregationResult:
        """
        Aggregate OHLC data from multiple sources using equal-weight averaging.
        
        Args:
            source_dataframes: Dict mapping source names to their DataFrames
            
        Returns:
            AggregationResult with aggregated data or error information
        """
        if not source_dataframes:
            return AggregationResult(
                success=False,
                error_details={"aggregation": "No source data provided"}
            )
        
        # Filter out empty DataFrames
        valid_sources = {
            source: df for source, df in source_dataframes.items() 
            if df is not None and not df.empty
        }
        
        if not valid_sources:
            return AggregationResult(
                success=False,
                sources_failed=list(source_dataframes.keys()),
                error_details={source: "Empty or None DataFrame" for source in source_dataframes.keys()}
            )
        
        logger.info(f"Aggregating data from {len(valid_sources)} sources: {list(valid_sources.keys())}")
        
        try:
            # Get common timestamps across all sources
            common_timestamps = DataAggregator._find_common_timestamps(valid_sources)
            
            if common_timestamps.empty:
                return AggregationResult(
                    success=False,
                    sources_failed=list(valid_sources.keys()),
                    error_details={"aggregation": "No common timestamps found across sources"}
                )
            
            # Aggregate data for common timestamps
            aggregated_df = DataAggregator._perform_aggregation(valid_sources, common_timestamps)
            
            return AggregationResult(
                success=True,
                data=aggregated_df,
                sources_used=list(valid_sources.keys()),
                metadata={
                    "aggregation_method": "equal_weight_averaging",
                    "common_timestamps_count": len(common_timestamps),
                    "total_sources": len(valid_sources)
                }
            )
            
        except Exception as e:
            logger.error(f"Aggregation failed: {str(e)}")
            return AggregationResult(
                success=False,
                sources_failed=list(valid_sources.keys()),
                error_details={"aggregation": f"Aggregation error: {str(e)}"}
            )
    
    @staticmethod
    def _find_common_timestamps(source_dataframes: Dict[str, pd.DataFrame]) -> pd.Index:
        """Find timestamps that exist in all source DataFrames."""
        if not source_dataframes:
            return pd.Index([])
        
        # Start with timestamps from first source
        common_timestamps = None
        
        for source, df in source_dataframes.items():
            if df is None or df.empty:
                continue
                
            current_timestamps = set(df['timestamp'].values)
            
            if common_timestamps is None:
                common_timestamps = current_timestamps
            else:
                common_timestamps = common_timestamps.intersection(current_timestamps)
        
        if common_timestamps is None:
            return pd.Index([])
        
        return pd.Index(sorted(common_timestamps))
    
    @staticmethod
    def _perform_aggregation(source_dataframes: Dict[str, pd.DataFrame], 
                           common_timestamps: pd.Index) -> pd.DataFrame:
        """
        Perform the actual aggregation using equal-weight averaging for OHLC and volume summing.
        """
        aggregated_data = []
        
        for timestamp in common_timestamps:
            # Collect data for this timestamp from all sources
            timestamp_data = []
            
            for source, df in source_dataframes.items():
                source_row = df[df['timestamp'] == timestamp]
                if not source_row.empty:
                    timestamp_data.append(source_row.iloc[0])
            
            if not timestamp_data:
                continue
            
            # Convert to DataFrame for easier manipulation
            timestamp_df = pd.DataFrame(timestamp_data)
            
            # Aggregate using equal-weight averaging for OHLC
            aggregated_row = {
                'timestamp': timestamp,
                'date': timestamp_df['date'].iloc[0],  # Use first date (should be same)
                'open': timestamp_df['open'].mean(),
                'high': timestamp_df['high'].mean(),
                'low': timestamp_df['low'].mean(),
                'close': timestamp_df['close'].mean(),
                'volume': timestamp_df['volume'].sum()  # Sum volumes
            }
            
            aggregated_data.append(aggregated_row)
        
        if not aggregated_data:
            return pd.DataFrame()
        
        # Create final DataFrame
        result_df = pd.DataFrame(aggregated_data)
        
        # Ensure proper data types
        result_df['timestamp'] = result_df['timestamp'].astype('int64')
        result_df['date'] = pd.to_datetime(result_df['date'])
        
        # Ensure timezone-aware dates
        if result_df['date'].dt.tz is None:
            result_df['date'] = result_df['date'].dt.tz_localize('UTC')
        else:
            result_df['date'] = result_df['date'].dt.tz_convert('UTC')
        
        # Convert OHLC to float64
        for col in ['open', 'high', 'low', 'close', 'volume']:
            result_df[col] = result_df[col].astype('float64')
        
        # Sort by timestamp descending (most recent first)
        result_df = result_df.sort_values('timestamp', ascending=False)
        
        return result_df
