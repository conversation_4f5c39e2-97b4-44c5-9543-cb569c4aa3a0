import re
from datetime import datetime
from fastapi import HTTPException
from app.logger.get_logger import log, logger
from app.config import VALID_TIMEFRAMES, MIN_CANDLES, MAX_CANDLES, DATE_FORMAT


@log
def validate_timeframe_or_raise(timeframe: str) -> str:
    """
    Validate timeframe parameter and return normalized version.

    Args:
        timeframe: String to validate

    Returns:
        str: Normalized timeframe (lowercase)

    Raises:
        HTTPException: 400 if invalid timeframe
    """
    if not timeframe:
        raise HTTPException(
                status_code = 400,
                detail = "Timeframe cannot be empty"
        )

    # Convert to lowercase for validation
    timeframe_lower = timeframe.lower()

    if timeframe_lower not in VALID_TIMEFRAMES:
        raise HTTPException(
                status_code = 400,
                detail = f"Invalid timeframe: '{timeframe}'. Valid timeframes are: {', '.join(VALID_TIMEFRAMES)}"
        )

    logger.info(f"Timeframe validation passed: {timeframe}")
    return timeframe_lower


@log
def validate_candles_or_raise(candles: str) -> int:
    """
    Validate candles parameter and return as integer.

    Args:
        candles: String to validate and convert

    Returns:
        int: Validated candles count

    Raises:
        HTTPException: 400 if invalid candles parameter
    """
    if not candles:
        raise HTTPException(
                status_code = 400,
                detail = "Candles parameter cannot be empty"
        )

    try:
        candles_int = int(candles)
    except ValueError:
        raise HTTPException(
                status_code = 400,
                detail = f"Invalid candles parameter: '{candles}'. Must be a positive integer"
        )

    if candles_int < MIN_CANDLES:
        raise HTTPException(
                status_code = 400,
                detail = f"Candles count too low: {candles_int}. Minimum is {MIN_CANDLES}"
        )

    if candles_int > MAX_CANDLES:
        raise HTTPException(
                status_code = 400,
                detail = f"Candles count too high: {candles_int}. Maximum is {MAX_CANDLES}"
        )

    logger.info(f"Candles validation passed: {candles_int}")
    return candles_int


@log
def validate_date_format_or_raise(date: str, param_name: str = "date") -> str:
    """
    Validate date format parameter.

    Args:
        date: String to validate
        param_name: Name of the parameter for error messages

    Returns:
        str: Validated date string

    Raises:
        HTTPException: 400 if invalid date format
    """
    if not date:
        raise HTTPException(
                status_code = 400,
                detail = f"{param_name} cannot be empty"
        )

    # Allow "now" as a special case
    if date.lower() == "now":
        logger.info(f"Date validation passed: {date} (special case)")
        return date.lower()

    # Validate date format
    try:
        datetime.strptime(date, DATE_FORMAT)
    except ValueError:
        raise HTTPException(
                status_code = 400,
                detail = f"Invalid {param_name} format: '{date}'. Expected format: '{DATE_FORMAT}' (e.g., '01012025') or 'now'"
        )

    logger.info(f"Date validation passed: {date}")
    return date
