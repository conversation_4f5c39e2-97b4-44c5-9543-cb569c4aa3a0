import pandas as pd
from typing import Dict, List
from app.logger.get_logger import log, logger
from app.core.aggregation_result import AggregationResult
from app.core.data_aggregator import DataAggregator
from app.core.discover_source_functions import discover_source_range_functions
from app.db.sqlite.get_from_db_by_timestamp import get_from_db_by_timestamp
from app.db.sqlite.store_in_db import store_in_db
from app.api.endpoints.get.helpers.is_data_complete import is_data_complete
from app.api.endpoints.get.helpers.get_missing_timestamps import get_missing_timestamps

from app.core.helpers.convert_date_to_utc_timestamp import convert_date_to_utc_timestamp
import pytz
from datetime import datetime


@log
def refresh_aggregated_db_by_range(currency_pair: str, timeframe: str, from_date: str, 
                                 to_date: str, candles: int) -> AggregationResult:
    """
    Refreshes aggregated databases from source databases for a specific date range.
    
    Args:
        currency_pair: Trading pair (e.g., 'btc-usd')
        timeframe: Time interval (e.g., 'm1', 'h1', 'd1')
        from_date: Start date in format 'ddmmyyyy'
        to_date: End date in format 'ddmmyyyy' or 'now'
        candles: Number of candles to aggregate
        
    Returns:
        AggregationResult with aggregated data or error information
    """
    logger.info(f"Starting range aggregation for {currency_pair} {timeframe} from {from_date} to {to_date}")
    
    # Convert dates to timestamps
    from_timestamp = convert_date_to_utc_timestamp(from_date)
    to_timestamp = convert_date_to_utc_timestamp(to_date) if to_date != "now" else None
    
    # Discover available sources
    discovered_sources = discover_source_range_functions()
    source_names = [name for name, _ in discovered_sources]
    
    if not source_names:
        return AggregationResult(
            success=False,
            error_details={"discovery": "No range source functions discovered"}
        )
    
    logger.info(f"Discovered sources: {source_names}")
    
    # Collect data from all source databases with strict validation
    source_dataframes = {}
    excluded_sources = []
    failed_sources = []
    error_details = {}
    source_completeness = {}

    for source_name in source_names:
        try:
            source_data = get_from_db_by_timestamp(
                currency_pair, timeframe, from_timestamp, to_timestamp, source_name
            )

            if source_data is not None and not source_data.empty:
                # Strict validation - requires 100% completeness
                validation_result = _validate_range_source_data_strict(
                    source_data, timeframe, from_timestamp, to_timestamp, candles
                )

                # Store completeness info for metadata
                source_completeness[source_name] = {
                    "completeness_percentage": validation_result["completeness"],
                    "actual_candles": validation_result["actual_candles"],
                    "expected_candles": validation_result["expected_candles"],
                    "errors": validation_result["errors"]
                }

                if validation_result["valid"]:
                    source_dataframes[source_name] = source_data
                    logger.info(f"✅ {source_name}: 100% complete ({len(source_data)}/{candles} candles)")
                else:
                    excluded_sources.append(source_name)
                    error_details[source_name] = f"Incomplete data: {validation_result['errors']}"
                    completeness = validation_result["completeness"]
                    logger.warning(f"❌ {source_name}: {completeness:.1f}% complete - EXCLUDED from aggregation")
            else:
                failed_sources.append(source_name)
                error_details[source_name] = "No data available in source database"
                source_completeness[source_name] = {"completeness_percentage": 0, "actual_candles": 0, "expected_candles": candles, "errors": ["No data"]}
                logger.warning(f"❌ {source_name}: No data found")

        except Exception as e:
            failed_sources.append(source_name)
            error_details[source_name] = f"Database access error: {str(e)}"
            source_completeness[source_name] = {"completeness_percentage": 0, "actual_candles": 0, "expected_candles": candles, "errors": [str(e)]}
            logger.error(f"❌ {source_name}: Database error - {str(e)}")
    
    # Check if we have any sources with 100% complete data
    if not source_dataframes:
        logger.warning(f"No sources provided 100% complete data for range {from_date} to {to_date}")
        return AggregationResult(
            success=False,
            sources_failed=failed_sources,
            error_details=error_details,
            metadata={
                "message": "No sources provided 100% complete data for range",
                "sources_excluded": excluded_sources,
                "source_completeness": source_completeness,
                "aggregation_strategy": "100% completeness required",
                "date_range": f"{from_date} to {to_date}"
            }
        )

    logger.info(f"Aggregating range data from {len(source_dataframes)} sources with 100% completeness: {list(source_dataframes.keys())}")
    if excluded_sources:
        logger.info(f"Excluded {len(excluded_sources)} incomplete sources: {excluded_sources}")

    # Perform aggregation with only 100% complete sources
    aggregation_result = DataAggregator.aggregate_ohlc_data(source_dataframes)

    # Update result with our tracking information
    aggregation_result.sources_failed.extend(failed_sources)
    aggregation_result.error_details.update(error_details)

    # Add detailed metadata about source selection
    if not aggregation_result.metadata:
        aggregation_result.metadata = {}

    aggregation_result.metadata.update({
        "sources_excluded": excluded_sources,
        "source_completeness": source_completeness,
        "aggregation_strategy": "100% completeness required",
        "total_sources_discovered": len(source_names),
        "sources_with_complete_data": len(source_dataframes),
        "date_range": f"{from_date} to {to_date}"
    })
    
    # If aggregation successful, store in aggregated database
    if aggregation_result.success and aggregation_result.data is not None:
        try:
            store_in_db(currency_pair, timeframe, aggregation_result.data, "aggregated")
            logger.info(f"Successfully stored aggregated range data in database")

            # Add storage confirmation to metadata
            aggregation_result.metadata["stored_in_aggregated_db"] = True
            aggregation_result.metadata["aggregated_records_count"] = len(aggregation_result.data)
            aggregation_result.metadata["date_range"] = f"{from_date} to {to_date}"
            
        except Exception as e:
            logger.error(f"Failed to store aggregated range data: {str(e)}")
            aggregation_result.metadata["storage_error"] = str(e)
    
    return aggregation_result


def _validate_range_source_data_strict(df: pd.DataFrame, timeframe: str, from_timestamp: int,
                                      to_timestamp: int, candles: int) -> Dict:
    """
    Strict validation for range source data - requires 100% completeness.
    Sources that don't provide 100% of requested data are excluded from aggregation.

    Args:
        df: Source DataFrame to validate
        timeframe: Expected timeframe
        from_timestamp: Start timestamp
        to_timestamp: End timestamp (can be None)
        candles: Expected number of candles

    Returns:
        Dict with validation result and detailed errors
    """
    errors = []

    # Check basic DataFrame properties
    if df is None:
        return {"valid": False, "errors": ["DataFrame is None"], "completeness": 0}

    if df.empty:
        return {"valid": False, "errors": ["DataFrame is empty"], "completeness": 0}

    # Check required columns
    required_columns = ['timestamp', 'date', 'open', 'high', 'low', 'close', 'volume']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        errors.append(f"Missing columns: {missing_columns}")

    # STRICT: Check exact number of candles
    actual_candles = len(df)
    if actual_candles != candles:
        errors.append(f"Incomplete data: expected {candles} candles, got {actual_candles}")

    # STRICT: Check for any missing timestamps in range
    missing_timestamps = get_missing_timestamps(df, timeframe, from_timestamp, to_timestamp)
    if missing_timestamps:
        errors.append(f"Missing timestamps: {len(missing_timestamps)} gaps detected")

    # STRICT: Check data completeness
    if not is_data_complete(df, candles):
        errors.append(f"Data completeness check failed")

    # Calculate completeness percentage
    completeness = (actual_candles / candles * 100) if candles > 0 else 0

    # For strict validation, ANY error means the source is excluded
    is_valid = len(errors) == 0

    return {
        "valid": is_valid,
        "errors": errors,
        "completeness": completeness,
        "actual_candles": actual_candles,
        "expected_candles": candles
    }
