from dataclasses import dataclass
from typing import List, Optional, Dict, Any
import pandas as pd


@dataclass
class AggregationResult:
    """
    Result of data aggregation from multiple sources.
    
    Attributes:
        success: Whether aggregation was successful
        data: Aggregated DataFrame (None if failed)
        sources_used: List of sources that contributed data
        sources_failed: List of sources that failed
        error_details: Detailed error information per source
        metadata: Additional metadata about the aggregation
    """
    success: bool
    data: Optional[pd.DataFrame] = None
    sources_used: List[str] = None
    sources_failed: List[str] = None
    error_details: Dict[str, str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        """Initialize empty lists and dicts if None."""
        if self.sources_used is None:
            self.sources_used = []
        if self.sources_failed is None:
            self.sources_failed = []
        if self.error_details is None:
            self.error_details = {}
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API response."""
        result = {
            "success": self.success,
            "sources_used": self.sources_used,
            "sources_failed": self.sources_failed,
            "error_details": self.error_details,
            "metadata": self.metadata
        }
        
        if self.data is not None:
            result["data"] = self.data.sort_values(by='timestamp', ascending=False).to_dict(orient='records')
        
        return result
