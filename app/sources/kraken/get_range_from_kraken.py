"""
Kraken Range Data Fetcher

This module fetches OHLC data for a specific date range from the Kraken API.
Since Kraken's OHLC endpoint doesn't support date range queries directly,
we use the 'since' parameter to fetch data from a specific timestamp.

Kraken API Limitations:
- Maximum 720 candles per request
- No direct date range support
- Uses 'since' parameter for historical data
"""

import json
import pandas as pd
from typing import Optional
from datetime import datetime

from app.sources.kraken.api.spot.market_data.get_ohlc_data import request
from app.sources.kraken.get_candles_from_kraken import (
    KRAKEN_TIMEFRAMES, KRAKEN_PAIRS, _parse_kraken_ohlc_data
)
from app.core.helpers.convert_date_to_utc_timestamp import convert_date_to_utc_timestamp
from app.logger.get_logger import log, logger


@log
def get_range_from_kraken(currency_pair: str, timeframe: str, from_date: str,
                          to_date: str, candles: int, ecc: bool = True) -> Optional[pd.DataFrame]:
    """
    Fetch OHLC data for a specific date range from Kraken API.
    
    Args:
        currency_pair: Trading pair (e.g., 'btcusd')
        timeframe: Time interval (e.g., 'm1', 'h1', 'd1')
        from_date: Start date in format 'ddmmyyyy'
        to_date: End date in format 'ddmmyyyy' or 'now'
        candles: Expected number of candles
        ecc: Exclude current candle (default: True)
        
    Returns:
        DataFrame with OHLC data or None if error
    """
    try:
        # Validate inputs
        if timeframe not in KRAKEN_TIMEFRAMES:
            logger.error(f"Unsupported timeframe for Kraken: {timeframe}")
            return None

        if currency_pair not in KRAKEN_PAIRS:
            logger.error(f"Unsupported currency pair for Kraken: {currency_pair}")
            return None

        # Convert dates to timestamps
        from_timestamp = convert_date_to_utc_timestamp(from_date)

        if to_date == "now":
            to_timestamp = int(datetime.utcnow().timestamp())
        else:
            to_timestamp = convert_date_to_utc_timestamp(to_date)

        logger.info(f"Fetching Kraken data for {currency_pair} {timeframe} from {from_date} to {to_date}")

        # Prepare API request
        kraken_pair = KRAKEN_PAIRS[currency_pair]
        kraken_interval = KRAKEN_TIMEFRAMES[timeframe]

        # Kraken uses 'since' parameter for historical data
        # We'll make multiple requests if needed due to 720 candle limit
        all_data = []
        current_since = from_timestamp

        while current_since < to_timestamp:
            # Make API request
            response = request(
                    method = "GET",
                    path = "/0/public/OHLC",
                    query = {
                            "pair"    : kraken_pair,
                            "interval": kraken_interval,
                            "since"   : current_since
                    },
                    environment = "https://api.kraken.com"
            )

            # Parse response
            response_data = response.read().decode()
            data = json.loads(response_data)

            # Check for errors
            if 'error' in data and data['error']:
                logger.error(f"Kraken API error: {data['error']}")
                break

            if 'result' not in data:
                logger.error("No result in Kraken API response")
                break

            # Extract OHLC data
            result = data['result']

            # Find the pair data
            ohlc_data = None
            for key, value in result.items():
                if key != 'last' and isinstance(value, list):
                    ohlc_data = value
                    break

            if not ohlc_data:
                logger.warning("No OHLC data in this batch")
                break

            # Parse the data
            batch_df = _parse_kraken_ohlc_data(ohlc_data, ecc = False)  # Don't exclude current yet

            if batch_df.empty:
                break

            # Filter data within our date range
            batch_df = batch_df[
                (batch_df['timestamp'] >= from_timestamp) &
                (batch_df['timestamp'] <= to_timestamp)
                ]

            if not batch_df.empty:
                all_data.append(batch_df)

            # Update since parameter for next request
            # Use the 'last' field from Kraken response if available
            if 'last' in result and result['last']:
                current_since = int(result['last'])
            else:
                # Fallback: use the last timestamp from current batch
                if not batch_df.empty:
                    current_since = int(batch_df['timestamp'].max()) + 1
                else:
                    break

            # Safety check to prevent infinite loops
            if len(all_data) > 100:  # Arbitrary limit
                logger.warning("Too many API requests, stopping")
                break

        # Combine all data
        if not all_data:
            logger.warning("No data retrieved from Kraken")
            return pd.DataFrame()

        combined_df = pd.concat(all_data, ignore_index = True)

        # Remove duplicates and sort
        combined_df = combined_df.drop_duplicates(subset = 'timestamp', keep = 'first')
        combined_df = combined_df.sort_values('timestamp').reset_index(drop = True)

        # Apply exclude current candle if requested
        if ecc and len(combined_df) > 0:
            # Remove the most recent candle
            combined_df = combined_df[:-1]

        logger.info(f"Successfully fetched {len(combined_df)} candles from Kraken for range")
        return combined_df

    except Exception as e:
        logger.error(f"Error fetching range data from Kraken: {str(e)}")
        return None


def get_kraken_historical_data(currency_pair: str, timeframe: str,
                               since_timestamp: int, limit: int = 720) -> Optional[pd.DataFrame]:
    """
    Get historical data from Kraken starting from a specific timestamp.
    
    Args:
        currency_pair: Trading pair
        timeframe: Time interval
        since_timestamp: Unix timestamp to start from
        limit: Maximum number of candles (max 720)
        
    Returns:
        DataFrame with historical data
    """
    try:
        if currency_pair not in KRAKEN_PAIRS or timeframe not in KRAKEN_TIMEFRAMES:
            return None

        kraken_pair = KRAKEN_PAIRS[currency_pair]
        kraken_interval = KRAKEN_TIMEFRAMES[timeframe]

        response = request(
                method = "GET",
                path = "/0/public/OHLC",
                query = {
                        "pair"    : kraken_pair,
                        "interval": kraken_interval,
                        "since"   : since_timestamp
                },
                environment = "https://api.kraken.com"
        )

        response_data = response.read().decode()
        data = json.loads(response_data)

        if 'error' in data and data['error']:
            logger.error(f"Kraken API error: {data['error']}")
            return None

        if 'result' not in data:
            return None

        result = data['result']

        # Find OHLC data
        ohlc_data = None
        for key, value in result.items():
            if key != 'last' and isinstance(value, list):
                ohlc_data = value
                break

        if not ohlc_data:
            return None

        return _parse_kraken_ohlc_data(ohlc_data, ecc = False)

    except Exception as e:
        logger.error(f"Error getting Kraken historical data: {str(e)}")
        return None


if __name__ == "__main__":
    # Test the function
    print("Testing Kraken range data fetch...")
    data = get_range_from_kraken('btcusd', 'h1', '01012024', '02012024', 24)
    if data is not None and not data.empty:
        print(f"✓ Successfully fetched {len(data)} candles")
        print(data.head())
        print(data.tail())
    else:
        print("✗ Failed to fetch range data")
