import pandas as pd
from typing import Optional

from app.core.helpers.convert_klines_to_date_range import convert_klines_to_date_range
from app.core.helpers.convert_timeframe_to_seconds import convert_timeframe_to_seconds
from app.logger.get_logger import log, logger
from app.sources.bitstamp.call_bitstamp import call_bitstamp
from app.sources.bitstamp.call_bitstamp_aggregated import call_bitstamp_aggregated

# Bitstamp currency pair mapping (new btc-usd format to Bitstamp format)
BITSTAMP_PAIRS = {
    'btc-usd': 'btcusd',       # Bitstamp uses btcusd
    'eth-usd': 'ethusd',       # Bitstamp uses ethusd
    'eth-btc': 'ethbtc',       # Bitstamp uses ethbtc
    'ltc-usd': 'ltcusd',       # Bitstamp uses ltcusd
    'ada-usd': 'adausd',       # Bitstamp uses adausd
    'dot-usd': 'dotusd',       # Bitstamp uses dotusd
    'xrp-usd': 'xrpusd',       # Bitstamp uses xrpusd
    'link-usd': 'linkusd',     # Bitstamp uses linkusd
    # Note: Bitstamp doesn't support USDC/USDT variants
}


def validate_bitstamp_pair(currency_pair: str) -> tuple[bool, str]:
    """
    Validate currency pair format and check Bitstamp support.

    Args:
        currency_pair: Format like 'btc-usd', 'eth-btc'

    Returns:
        Tuple of (is_valid, error_message)
    """
    if '-' not in currency_pair:
        return False, "Invalid format. Use 'base-quote' format (e.g., 'btc-usd')"

    if currency_pair not in BITSTAMP_PAIRS:
        supported = list(BITSTAMP_PAIRS.keys())
        return False, f"Currency pair {currency_pair} not supported by Bitstamp. Supported: {supported}"

    # Check for unsupported USD variants
    if currency_pair.endswith('-usdc') or currency_pair.endswith('-usdt'):
        return False, f"Bitstamp doesn't support USDC/USDT variants. Use {currency_pair.replace('-usdc', '-usd').replace('-usdt', '-usd')} instead"

    return True, ""


@log
def get_candles_from_bitstamp(currency_pair: str, timeframe: str, candles: int, ecc: bool = True) -> Optional[pd.DataFrame]:
    """
    Fetch OHLC candle data from Bitstamp API.

    Args:
        currency_pair: Trading pair in new format (e.g., 'btc-usd')
        timeframe: Time interval (e.g., 'm1', 'h1', 'd1')
        candles: Number of candles to fetch
        ecc: Exclude current candle (default: True)

    Returns:
        DataFrame with OHLC data or None if error
    """
    try:
        # Validate currency pair format and Bitstamp support
        is_valid, error_msg = validate_bitstamp_pair(currency_pair)
        if not is_valid:
            logger.error(f"Bitstamp validation failed: {error_msg}")
            return None

        # Convert to Bitstamp format
        bitstamp_pair = BITSTAMP_PAIRS[currency_pair]
        logger.info(f"Fetching {candles} {timeframe} candles for {currency_pair} ({bitstamp_pair}) from Bitstamp")

        if candles > 1000:
            start_date = convert_klines_to_date_range(timeframe, candles)[0]
            external_data = call_bitstamp_aggregated(bitstamp_pair, timeframe, start_date, None)
        else:
            external_data = call_bitstamp(
                    bitstamp_pair, convert_timeframe_to_seconds(timeframe), candles, None, None, ecc
            )

        if external_data is not None and not external_data.empty:
            logger.info(f"Successfully fetched {len(external_data)} candles from Bitstamp")
        else:
            logger.warning(f"No data returned from Bitstamp for {currency_pair}")

        return external_data

    except Exception as e:
        logger.error(f"Error fetching data from Bitstamp: {str(e)}")
        return None


def get_supported_bitstamp_pairs() -> list:
    """Get list of supported currency pairs for Bitstamp."""
    return list(BITSTAMP_PAIRS.keys())
