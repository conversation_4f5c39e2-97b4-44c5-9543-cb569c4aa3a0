import pandas as pd

from app.core.helpers.convert_date_to_utc_timestamp import convert_date_to_utc_timestamp
from app.core.helpers.convert_timeframe_to_seconds import convert_timeframe_to_seconds
from app.logger.get_logger import log
from app.sources.bitstamp.call_bitstamp import call_bitstamp
from app.sources.bitstamp.call_bitstamp_aggregated import call_bitstamp_aggregated


@log
def get_range_from_bitstamp(currency_pair: str, timeframe: str, from_date: str, to_date: str, candles: int, ecc: bool) -> pd.DataFrame:
    if candles > 1000:
        df = call_bitstamp_aggregated(currency_pair, timeframe, from_date, to_date, ecc)
    else:
        from_timestamp = convert_date_to_utc_timestamp(from_date)
        to_timestamp = convert_date_to_utc_timestamp(to_date)
        df = call_bitstamp(
                currency_pair, convert_timeframe_to_seconds(timeframe), candles, from_timestamp, to_timestamp, ecc
        )

    return df
