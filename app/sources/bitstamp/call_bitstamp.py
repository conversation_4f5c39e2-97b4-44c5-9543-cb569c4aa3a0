import pandas as pd
import requests
from ratelimit import limits

from app.sources.bitstamp import URL_BITSTAMP
from app.logger.get_logger import log

"""
Bitstamp API - FREE
Documentation: https://www.bitstamp.net/api/
Pricing: Free to use
No API key required for public endpoints (including OHLC)
Rate limits: 8000 requests per 10 minutes
"""
@log
@limits(calls = 7900, period = 600)
def call_bitstamp(market_symbol, step, limit, from_timestamp, to_timestamp, exclude_current_candle) -> pd.DataFrame:
    """
    https://www.bitstamp.net/api/#tag/Market-info/operation/GetOHLCData
    Downloads OHLC data for a given currency pair and time period from Bitstamp.

    Parameters
    ----------
    market_symbol : str
        The market symbol for the currency pair. Example: "btcusd".
    step : int | str
        The time period for the candlestick data. Example: 60 (1 minute).
    limit : int | str
        The maximum number of candlesticks to retrieve backwards from to_timestamp. Example: 1000.
    from_timestamp : int | str | None
        The start of the time period for which to retrieve data. Example: 1598059580.
    to_timestamp : int | str | None
        The end of the time period for which to retrieve data. Example: 1598063180.
    exclude_current_candle : bool
        Whether to exclude the current candle from the data. Example: False.

    Returns
    -------
    pd.DataFrame
        The retrieved data, with columns "timestamp", "date", "open", "high", "low", "close", and "volume".
        The data is sorted in descending order by timestamp and has duplicate timestamps removed.
    """
    if not all([market_symbol, step, limit]):
        raise ValueError("Bistamp call: market_symbol, step, and limit are required parameters")

    url = f"{URL_BITSTAMP}{market_symbol}/"
    params = {
            "step"                  : step,
            "limit"                 : limit,
            "start"                 : from_timestamp,
            "end"                   : to_timestamp,
            "exclude_current_candle": exclude_current_candle,
    }

    data = requests.get(url, params = params)
    data = data.json()["data"]["ohlc"]
    df = pd.DataFrame(data)
    df["date"] = pd.to_datetime(df.timestamp.astype(int), unit = 's')
    df["date"] = pd.to_datetime(df["date"])
    df["timestamp"] = df["timestamp"].astype("int64")
    df = df[["timestamp", "date", "open", "high", "low", "close", "volume"]]
    df = df.sort_values(by = "timestamp", ascending = False)
    df = df.drop_duplicates(subset = "timestamp", keep = "first")

    return df

# # example:
# print(call_bitstamp("btcusd", 60, 1000, 1598059580, 1598063180, False))
## # output:
#       timestamp                date      open  ...       low     close       volume
# 999  1598063160 2020-08-22 02:26:00  11438.58  ...  11432.93  11434.02   0.85649763
# 998  1598063100 2020-08-22 02:25:00  11429.62  ...  11429.62  11429.82   0.14436679
# 997  1598063040 2020-08-22 02:24:00  11432.16  ...  11424.51  11431.54   2.37073850
# 996  1598062980 2020-08-22 02:23:00  11432.68  ...  11426.64  11428.69   4.25789935
# 995  1598062920 2020-08-22 02:22:00  11440.92  ...  11430.49  11433.39  13.06771818
# ..          ...                 ...       ...  ...       ...       ...          ...
# 4    1598003460 2020-08-21 09:51:00  11789.74  ...  11785.46  11793.88   3.57596011
# 3    1598003400 2020-08-21 09:50:00  11795.52  ...  11790.43     11794   0.81028438
# 2    1598003340 2020-08-21 09:49:00  11796.11  ...  11794.39  11802.99   0.59310967
# 1    1598003280 2020-08-21 09:48:00  11804.67  ...  11796.17  11799.06   1.48345942
# 0    1598003220 2020-08-21 09:47:00  11804.42  ...  11796.98  11797.67   3.01307140
#
# [1000 rows x 7 columns]


# print(call_bitstamp("btcusd", 60, 2, 1598059560, 1598059560, False))
# output:
#       timestamp                date      open  ...       low     close       volume
# 1  1598059560 2020-08-22 01:26:00  11451.06  ...  11441.04  11443.24  3.19375511
# 0  1598059500 2020-08-22 01:25:00  11439.56  ...  11436.23  11451.06  4.11888785
#
# [2 rows x 7 columns]


# print(call_bitstamp("btcusd", 60, 2, 1598059500, 1598059560, False))
# output:
#     timestamp                date      open  ...       low     close      volume
# 1  1598059560 2020-08-22 01:26:00  11451.06  ...  11441.04  11443.24  3.19375511
# 0  1598059500 2020-08-22 01:25:00  11439.56  ...  11436.23  11451.06  4.11888785
#
# [2 rows x 7 columns]
#
