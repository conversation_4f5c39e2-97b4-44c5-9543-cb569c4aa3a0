import pandas as pd

from datetime import datetime
from app.logger.get_logger import log, logger
from app.sources.coindesk.minute import get_historical_minutes
from app.sources.coindesk.hourly import get_historical_hours
from app.sources.coindesk.daily import get_historical_days

"""
Coindesk API Client
Documentation: https://developers.coindesk.com/documentation/data-api

Available data:
- Crypto prices and market data
- OHLCV data (minute to daily)
- Market aggregates
- Exchange data
- News and insights
- Historical data

Pricing (as of 2024):
- Free tier: Basic access with rate limits
- Professional: Custom pricing based on usage
- Enterprise: Custom solutions and dedicated support

API key required for most endpoints
Visit https://data.coindesk.com for current pricing and features
"""


def _convert_currency_pair_format(currency_pair: str) -> str:
    """
    Convert currency pair from internal format (btc-usd) to Coindesk format (BTC-USD).

    Args:
        currency_pair: Internal pair format (e.g., 'btc-usd', 'eth-btc')

    Returns:
        str: Coindesk API format (e.g., 'BTC-USD', 'ETH-BTC')
    """
    if '-' not in currency_pair:
        raise ValueError(f"Invalid currency pair format: {currency_pair}. Expected format: 'base-quote'")

    base, quote = currency_pair.upper().split('-')
    return f"{base}-{quote}"


def validate_coindesk_pair(currency_pair: str) -> tuple[bool, str]:
    """
    Validate currency pair format for Coindesk.

    Args:
        currency_pair: Format like 'btc-usd'

    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        _convert_currency_pair_format(currency_pair)
        return True, ""
    except ValueError as e:
        return False, str(e)


@log
def get_candles_from_coindesk(currency_pair: str, timeframe: str, candles: int,
                              ecc: bool = True, market: str = "binanceaggregate") -> pd.DataFrame:
    """
    Main function to retrieve OHLC data from Coindesk API.
    Makes multiple calls to aggregate the data with appropriate limits based on timeframe.

    Parameters
    ----------
    currency_pair : str
        The trading pair in new format (e.g., 'btc-usd')
    timeframe : str
        The timeframe for data points ('m1', 'm15' for minutes, 'h1', 'h4' for hours, 'd1' for daily)
    candles : int
        Number of candles to retrieve
    ecc : bool, optional
        Exclude current candle flag (default: True)
    market : str, optional
        Market identifier for Coindesk API (default: "bitfinex")

    Returns
    -------
    pd.DataFrame
        df with columns: timestamp, date, open, high, low, close, volume
    """
    # Map timeframes to functions and aggregation values
    timeframe_functions = {
            "m1" : (get_historical_minutes, 1, 2000),
            "m5" : (get_historical_minutes, 5, 400),
            "m15": (get_historical_minutes, 15, 133),
            "m30": (get_historical_minutes, 30, 66),
            "h1" : (get_historical_hours, 1, 2000),
            "h4" : (get_historical_hours, 4, 500),
            "d1" : (get_historical_days, 1, 2000)
    }

    if timeframe not in timeframe_functions:
        raise ValueError(f"Invalid timeframe. Must be one of: {', '.join(timeframe_functions.keys())}")

    # Validate currency pair format and Coindesk support
    is_valid, error_msg = validate_coindesk_pair(currency_pair)
    if not is_valid:
        logger.error(f"Coindesk validation failed: {error_msg}")
        return pd.DataFrame()

    # Convert to Coindesk format
    instrument = _convert_currency_pair_format(currency_pair)
    logger.info(f"Fetching {candles} {timeframe} candles for {currency_pair} ({instrument}) from Coindesk")

    master_data = []
    current_timestamp = int(datetime.now().timestamp())

    func, aggregation, limit = timeframe_functions[timeframe]

    # Coindesk API has a limit of 2000 candles per request for m1, h1, d1
    max_limit_per_request = limit
    returned_candles = 0

    # If ecc is True, we need to request one additional candle to compensate
    # for the one that will be removed, but don't exceed the max limit
    target_candles = candles
    if ecc and candles < max_limit_per_request:
        target_candles += 1

    while target_candles > returned_candles:
        # Make the API call
        df = func(
                instrument = instrument,
                market = market,
                aggregate = aggregation,
                limit = max_limit_per_request,
                to_ts = current_timestamp
        )

        if df.empty:
            break

        # Update the timestamp for the next request
        current_timestamp = df['timestamp'].min()
        returned_candles += len(df)
        master_data.append(df)

    if not master_data:
        return pd.DataFrame()

    # Combine all the data
    df = pd.concat(master_data, ignore_index = True)
    df = df[["timestamp", "date", "open", "high", "low", "close", "volume"]]
    df = df.sort_values(by = "timestamp", ascending = False)
    df = df.drop_duplicates(subset = "timestamp", keep = "first")

    # Drop the newest candle if ecc is True
    if ecc and not df.empty:
        df = df.iloc[1:]

    return df


def get_supported_coindesk_pairs() -> list:
    """
    Get list of commonly supported currency pairs for Coindesk.
    Note: Coindesk primarily supports BTC/USD, but other pairs may be available.
    """
    # Common pairs that Coindesk typically supports
    return ['btc-usd']


if __name__ == "__main__":
    # Test configuration - easily changeable parameters
    TEST_CURRENCY_PAIR = 'eth-usd'
    TEST_TIMEFRAME = 'h1'
    TEST_CANDLES = 10
    TEST_ECC = True
    TEST_MARKET = 'bitfinex'  # Try different markets: bitfinex, binance, kraken, etc.

    print("Testing Coindesk API...")
    print(
        f"Configuration: {TEST_CURRENCY_PAIR}, {TEST_TIMEFRAME}, {TEST_CANDLES} candles, ECC={TEST_ECC}, Market={TEST_MARKET}")
    print("-" * 60)

    try:
        # Test the main function
        data = get_candles_from_coindesk(
                currency_pair = TEST_CURRENCY_PAIR,
                timeframe = TEST_TIMEFRAME,
                candles = TEST_CANDLES,
                ecc = TEST_ECC,
                market = TEST_MARKET
        )

        if data is not None and not data.empty:
            print(f"✓ Successfully fetched {len(data)} candles")
            print(f"  Timeframe: {TEST_TIMEFRAME}")
            print(f"  Currency pair: {TEST_CURRENCY_PAIR}")
            print(f"  Price range: ${data['low'].min():.2f} - ${data['high'].max():.2f}")
            print(f"  Latest timestamp: {data['timestamp'].max()} as date: {data['date'].max()}")
            print(f"  Oldest timestamp: {data['timestamp'].min()} as date: {data['date'].min()}")
            print("\nFirst 3 candles (newest to oldest):")
            print(
                data[['timestamp', 'date', 'open', 'high', 'low', 'close', 'volume']].head(3).to_string(index = False))
        else:
            print(f"✗ Failed to fetch data for {TEST_CURRENCY_PAIR}")

    except Exception as e:
        print(f"✗ Error occurred: {str(e)}")

    print("\n" + "=" * 60)
    print("Test different timeframes:")

    # Test different timeframes
    test_timeframes = ['m15', 'h1', 'h4']
    for tf in test_timeframes:
        try:
            print(f"\nTesting {tf}...")
            data = get_candles_from_coindesk(TEST_CURRENCY_PAIR, tf, 5, TEST_ECC, TEST_MARKET)
            if data is not None and not data.empty:
                print(f"  ✓ {tf}: {len(data)} candles, latest: {data['close'].iloc[0]:.2f}")
            else:
                print(f"  ✗ {tf}: No data")
        except Exception as e:
            print(f"  ✗ {tf}: Error - {str(e)}")
