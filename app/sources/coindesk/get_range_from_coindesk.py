"""
Coindesk API Client
Documentation: https://developers.coindesk.com/documentation/data-api

Available data:
- Crypto prices and market data
- OHLCV data (minute to daily)
- Market aggregates
- Exchange data
- News and insights
- Historical data

Pricing (as of 2024):
- Free tier: Basic access with rate limits
- Professional: Custom pricing based on usage
- Enterprise: Custom solutions and dedicated support

API key required for most endpoints
Visit https://data.coindesk.com for current pricing and features
"""

from datetime import datetime
import pandas as pd
from app.logger.get_logger import log, logger
from app.sources.coindesk.minute import get_historical_minutes
from app.sources.coindesk.hourly import get_historical_hours
from app.sources.coindesk.daily import get_historical_days
from app.core.helpers.convert_date_to_utc_timestamp import convert_date_to_utc_timestamp


def _convert_currency_pair_format(currency_pair: str) -> str:
    """
    Convert currency pair from internal format (btc-usd) to Coindesk format (BTC-USD).

    Args:
        currency_pair: Internal pair format (e.g., 'btc-usd', 'eth-btc')

    Returns:
        str: Coindesk API format (e.g., 'BTC-USD', 'ETH-BTC')
    """
    if '-' not in currency_pair:
        raise ValueError(f"Invalid currency pair format: {currency_pair}. Expected format: 'base-quote'")

    base, quote = currency_pair.upper().split('-')
    return f"{base}-{quote}"


@log
def get_range_from_coindesk(currency_pair: str, timeframe: str, from_date: str, to_date: str, candles: int,
                            ecc: bool, market: str = "bitfinex") -> pd.DataFrame:
    """
    Main function to retrieve OHLC data from Coindesk API for a date range.
    Makes multiple calls to aggregate the data with appropriate limits based on timeframe.

    Parameters
    ----------
    currency_pair : str
        The trading pair (e.g., 'btcusd')
    timeframe : str
        The timeframe for data points ('m1', 'm5', 'm15', 'm30' for minutes, 'h1', 'h4' for hours, 'd1' for daily)
    from_date : str
        Start date in format 'ddmmyyyy'
    to_date : str
        End date in format 'ddmmyyyy' (default: None, which gets data up to now)
    candles : int
        Number of candles to retrieve (used as a limit if date range would return more candles)
    ecc : bool
        Exclude current candle flag
    market : str, optional
        Market identifier for Coindesk API (default: "bitfinex")

    Returns
    -------
    pd.DataFrame
        df with columns: timestamp, date, open, high, low, close, volume

    Raises
    ------
    ValueError
        If an invalid timeframe is provided or date format is incorrect
    """
    # Define timeframe functions and their aggregation values
    timeframe_functions = {
            "m1" : (get_historical_minutes, 1),
            "m5" : (get_historical_minutes, 5),
            "m15": (get_historical_minutes, 15),
            "m30": (get_historical_minutes, 30),
            "h1" : (get_historical_hours, 1),
            "h4" : (get_historical_hours, 4),
            "d1" : (get_historical_days, 1)
    }

    if timeframe not in timeframe_functions:
        raise ValueError(f"Invalid timeframe. Must be one of: {', '.join(timeframe_functions.keys())}")

    if not from_date:
        raise ValueError("Start date must be provided")

    try:
        datetime.strptime(from_date, "%d%m%Y")
    except ValueError:
        raise ValueError("Start date must be in the format 'ddmmyyyy'")

    if not to_date:
        to_date = "now"

    # Validate and convert currency pair format
    try:
        instrument = _convert_currency_pair_format(currency_pair)
        logger.info(f"Fetching range data for {currency_pair} ({instrument}) from Coindesk")
    except ValueError as e:
        logger.error(f"Coindesk validation failed: {str(e)}")
        return pd.DataFrame()

    master_data = []
    from_timestamp = convert_date_to_utc_timestamp(from_date)
    to_timestamp = convert_date_to_utc_timestamp(to_date)

    func, aggregation = timeframe_functions[timeframe]
    min_timestamp = to_timestamp

    # Coindesk API has a limit of 2000 candles per request
    max_limit_per_request = min(2000, candles)
    returned_candles = 0

    while min_timestamp > from_timestamp and returned_candles < candles:
        df = func(
                instrument = instrument,
                market = market,
                aggregate = aggregation,
                limit = max_limit_per_request,
                to_ts = to_timestamp
        )

        if df.empty:
            break

        master_data.append(df)
        returned_candles += len(df)
        min_timestamp = df['timestamp'].min()
        to_timestamp = min_timestamp

    if not master_data:
        return pd.DataFrame()

    df = pd.concat(master_data, ignore_index = True)
    df = df[["timestamp", "date", "open", "high", "low", "close", "volume"]]
    df = df.sort_values(by = "timestamp", ascending = False)
    df = df.drop_duplicates(subset = "timestamp", keep = "first")

    # Filter data to only include dates within the requested range
    df = df[df['timestamp'] >= from_timestamp]

    # Limit to the requested number of candles
    if len(df) > candles:
        df = df.head(candles)

    # Drop the newest candle if ecc is True
    if ecc and not df.empty:
        df = df.iloc[1:]

    return df


if __name__ == "__main__":
    # Test configuration - easily changeable parameters
    TEST_CURRENCY_PAIR = 'btc-usd'
    TEST_TIMEFRAME = 'h1'
    TEST_FROM_DATE = '01012024'  # ddmmyyyy format
    TEST_TO_DATE = '02012024'  # ddmmyyyy format
    TEST_CANDLES = 50
    TEST_ECC = True
    TEST_MARKET = 'bitfinex'  # Try different markets: bitfinex, binance, kraken, etc.

    print("Testing Coindesk Range API...")
    print(f"Configuration: {TEST_CURRENCY_PAIR}, {TEST_TIMEFRAME}")
    print(f"Date range: {TEST_FROM_DATE} to {TEST_TO_DATE}")
    print(f"Max candles: {TEST_CANDLES}, ECC={TEST_ECC}, Market={TEST_MARKET}")
    print("-" * 60)

    try:
        # Test the main function
        data = get_range_from_coindesk(
                currency_pair = TEST_CURRENCY_PAIR,
                timeframe = TEST_TIMEFRAME,
                from_date = TEST_FROM_DATE,
                to_date = TEST_TO_DATE,
                candles = TEST_CANDLES,
                ecc = TEST_ECC,
                market = TEST_MARKET
        )

        if data is not None and not data.empty:
            print(f"✓ Successfully fetched {len(data)} candles")
            print(f"  Timeframe: {TEST_TIMEFRAME}")
            print(f"  Currency pair: {TEST_CURRENCY_PAIR}")
            print(f"  Price range: ${data['low'].min():.2f} - ${data['high'].max():.2f}")
            print(f"  Date range: {data['date'].min()} to {data['date'].max()}")
            print("\nFirst 3 candles (newest to oldest):")
            print(
                data[['timestamp', 'date', 'open', 'high', 'low', 'close', 'volume']].head(3).to_string(index = False))
        else:
            print(f"✗ Failed to fetch data for {TEST_CURRENCY_PAIR}")

    except Exception as e:
        print(f"✗ Error occurred: {str(e)}")

    print("\n" + "=" * 60)
    print("Test different date ranges:")

    # Test different date ranges
    test_ranges = [
            ('01122023', '02122023'),  # December 2023
            ('15012024', '16012024'),  # January 2024
    ]

    for from_d, to_d in test_ranges:
        try:
            print(f"\nTesting range {from_d} to {to_d}...")
            data = get_range_from_coindesk(TEST_CURRENCY_PAIR, 'h4', from_d, to_d, 20, TEST_ECC, TEST_MARKET)
            if data is not None and not data.empty:
                print(f"  ✓ Range {from_d}-{to_d}: {len(data)} candles")
                print(f"    Latest price: {data['close'].iloc[0]:.2f}")
            else:
                print(f"  ✗ Range {from_d}-{to_d}: No data")
        except Exception as e:
            print(f"  ✗ Range {from_d}-{to_d}: Error - {str(e)}")
