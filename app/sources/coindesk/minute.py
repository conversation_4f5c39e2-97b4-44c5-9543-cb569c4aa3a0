from typing import Optional

import pandas as pd
from ratelimit import limits
from app.logger.get_logger import log
from app.sources.coindesk.request import make_request


@log
@limits(calls = 100000, period = 2592000)
def get_historical_minutes(instrument: str = "BTC-USD",
                           market: str = "ccix",
                           limit: int = 2000,
                           aggregate: int = 1,
                           fill: str = "true",
                           apply_mapping: str = "true",
                           response_format: str = "JSON",
                           groups: str = "OHLC,VOLUME",
                           to_ts: Optional[int] = None,
                           start_time: Optional[str] = None,
                           end_time: Optional[str] = None) -> pd.DataFrame:
    """
    Get historical minute OHLCV data from Coindesk API

    Parameters
    ----------
    instrument : str
        The trading pair (e.g., 'BTC-USD')
    market : str
        Market identifier (default: 'cadli')
    limit : int
        Number of data points (max 2000)
    aggregate : int
        Aggregation level in minutes (1, 5, 15, 30)
    fill : str
        Whether to fill missing data points (default: 'true')
    apply_mapping : str
        Whether to apply mapping to the response (default: 'true')
    response_format : str
        Format of the response (default: 'JSON')
    groups : str
        Data groups to include (default: 'OHLC')
    to_ts : Optional[int]
        End timestamp in Unix time format
    start_time : Optional[str]
        Start time in ISO 8601 format (e.g., '2024-01-01T00:00:00Z')
    end_time : Optional[str]
        End time in ISO 8601 format (e.g., '2024-01-31T23:59:59Z')

    Returns
    -------
    pd.DataFrame
        OHLCV data with columns: timestamp, date, open, high, low, close, volume

    Examples
    --------
    Get 15-minute aggregated candles:

    >>> df = get_historical_minutes(instrument='BTC-USD', aggregate=15, limit=100)
    >>> isinstance(df, pd.DataFrame)
    True
    >>> list(df.columns)
    ['timestamp', 'date', 'open', 'high', 'low', 'close', 'volume']
    """
    params = {
            "market"         : market,
            "instrument"     : instrument,
            "limit"          : limit,
            "aggregate"      : aggregate,
            "fill"           : fill,
            "apply_mapping"  : apply_mapping,
            "response_format": response_format,
            "groups"         : groups
    }

    if to_ts is not None:
        params['to_ts'] = to_ts

    if start_time:
        params['start_time'] = start_time
    if end_time:
        params['end_time'] = end_time

    return make_request("/historical/minutes", params)


# Example usage
# data = get_historical_minutes(limit = 30)
# print(data)

# # Get 15-minute aggregated candles
# data = get_historical_minutes(
#     instrument="BTC-USD",
#     aggregate=15,
#     limit=100
# )
# print(data)
