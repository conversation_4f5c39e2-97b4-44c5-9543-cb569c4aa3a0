import pandas as pd
import requests
from typing import Dict, Any
from ratelimit import limits
from app.sources.coindesk import URL
from app.logger.get_logger import log


@limits(calls=100000, period=2592000)
@log
def make_request(endpoint: str, params: Dict[str, Any]) -> pd.DataFrame:
    """
    Makes a request to the Coindesk API and formats the response.

    Parameters
    ----------
    endpoint : str
        API endpoint
    params : Dict[str, Any]
        Request parameters

    Returns
    -------
    pd.DataFrame
        Formatted OHLCV data with columns: timestamp, date, open, high, low, close, volume
    """
    url = f"{URL}{endpoint}"
    headers = {"Content-type": "application/json; charset=UTF-8"}

    response = requests.get(url, params=params, headers=headers)
    response.raise_for_status()

    data = response.json()

    # Check for errors in the response
    if 'Err' in data and data['Err']:
        raise ValueError(f"Coindesk API Error: {data['Err']}")

    # Create DataFrame from the Data field
    df = pd.DataFrame(data['Data'])

    # Convert column names to lowercase
    df.columns = df.columns.str.lower()

    # Create date column from timestamp (UTC timezone-aware)
    df['date'] = pd.to_datetime(df['timestamp'].astype(int), unit='s', utc=True)

    # Add volume column if it doesn't exist (set to 0)
    if 'volume' not in df.columns:
        df['volume'] = 0

    # Select and rename required columns
    columns_to_select = ['timestamp', 'date', 'open', 'high', 'low', 'close', 'volume']

    # Ensure all required columns exist
    for col in columns_to_select:
        if col not in df.columns and col != 'date':  # date is created above
            df[col] = 0

    # Select the columns in the desired order
    df = df[columns_to_select]

    # Convert timestamp to int64
    df['timestamp'] = df['timestamp'].astype('int64')

    # Sort by timestamp in descending order
    df = df.sort_values(by='timestamp', ascending=False)

    # Remove any duplicate timestamps
    df = df.drop_duplicates(subset='timestamp', keep='first')

    return df
