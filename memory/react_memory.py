"""
Clean memory manager for ReAct agent framework.
"""
import sqlite3
import json
from datetime import datetime
from typing import Dict, Any, List
from logger.get_logger import log


class ReActMemoryManager:
    """
    Clean memory manager for ReAct agent.
    Stores conversation history and reasoning steps.
    """
    
    def __init__(self, db_path: str = "memory/react_memory.db", max_tokens: int = 3500):
        """
        Initialize memory manager.
        
        Args:
            db_path: Path to SQLite database
            max_tokens: Maximum tokens for context
        """
        self.db_path = db_path
        self.max_tokens = max_tokens
        self._init_db()
        
    def _init_db(self):
        """Initialize the SQLite database with clean schema."""
        with sqlite3.connect(self.db_path) as conn:
            # Conversations table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    role TEXT NOT NULL,
                    content TEXT NOT NULL,
                    reasoning TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Reasoning steps table for detailed tracking
            conn.execute("""
                CREATE TABLE IF NOT EXISTS reasoning_steps (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    conversation_id INTEGER,
                    step_number INTEGER,
                    thought TEXT,
                    action TEXT,
                    action_input TEXT,
                    observation TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (conversation_id) REFERENCES conversations (id)
                )
            """)

    @log
    def load_memory_variables(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Load memory variables for ReAct agent.
        
        Args:
            inputs: Input dictionary containing user input
            
        Returns:
            Dictionary with chat history and context
        """
        context = self._get_recent_context()
        return {
            "chat_history": context,
            "context_length": len(context.split()) if context else 0
        }

    def _get_recent_context(self) -> str:
        """Get recent conversation context from the database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT role, content FROM conversations
                    ORDER BY timestamp DESC
                    LIMIT 10
                """)
                messages = cursor.fetchall()
        except Exception as e:
            print(f"Error loading memory: {e}")
            return ""

        if not messages:
            return ""

        # Reverse to get chronological order
        messages = list(reversed(messages))

        # Format messages with token limit
        context_parts = []
        total_length = 0

        for role, content in messages:
            message_text = f"{role.title()}: {content}"
            message_length = len(message_text.split())

            # Simple token estimation (1 token ≈ 0.75 words)
            estimated_tokens = int(message_length * 1.33)
            
            if total_length + estimated_tokens > self.max_tokens:
                break

            context_parts.append(message_text)
            total_length += estimated_tokens

        return "\n\n".join(context_parts)

    @log
    def save_context(self, inputs: Dict[str, Any], outputs: Dict[str, str]) -> None:
        """
        Save conversation context and reasoning steps.
        
        Args:
            inputs: Input dictionary with user input
            outputs: Output dictionary with agent response and reasoning
        """
        user_message = inputs.get("input", "")
        ai_message = outputs.get("output", "")
        reasoning = outputs.get("reasoning", "")

        if not user_message or not ai_message:
            return

        try:
            with sqlite3.connect(self.db_path) as conn:
                # Save user message
                cursor = conn.execute("""
                    INSERT INTO conversations (role, content, reasoning) 
                    VALUES (?, ?, ?)
                """, ("user", user_message, ""))
                
                # Save assistant message with reasoning
                cursor = conn.execute("""
                    INSERT INTO conversations (role, content, reasoning) 
                    VALUES (?, ?, ?)
                """, ("assistant", ai_message, reasoning))
                
                print(f"💾 Saved conversation to memory")
                
        except Exception as e:
            print(f"Error saving memory: {e}")

    def save_reasoning_steps(self, conversation_id: int, reasoning_steps: List[Dict[str, Any]]):
        """
        Save detailed reasoning steps for analysis.
        
        Args:
            conversation_id: ID of the conversation
            reasoning_steps: List of reasoning step dictionaries
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                for step in reasoning_steps:
                    conn.execute("""
                        INSERT INTO reasoning_steps 
                        (conversation_id, step_number, thought, action, action_input, observation)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        conversation_id,
                        step.get("step_number", 0),
                        step.get("thought", ""),
                        step.get("action", ""),
                        step.get("action_input", ""),
                        step.get("observation", "")
                    ))
        except Exception as e:
            print(f"Error saving reasoning steps: {e}")

    def clear(self) -> None:
        """Clear all memory."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("DELETE FROM conversations")
                conn.execute("DELETE FROM reasoning_steps")
            print("🧹 Memory cleared")
        except Exception as e:
            print(f"Error clearing memory: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """Get memory statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Get conversation count
                cursor = conn.execute("SELECT COUNT(*) FROM conversations")
                message_count = cursor.fetchone()[0]
                
                # Get reasoning steps count
                cursor = conn.execute("SELECT COUNT(*) FROM reasoning_steps")
                reasoning_count = cursor.fetchone()[0]
                
                # Get recent reasoning patterns
                cursor = conn.execute("""
                    SELECT action, COUNT(*) as count 
                    FROM reasoning_steps 
                    WHERE action != '' 
                    GROUP BY action 
                    ORDER BY count DESC 
                    LIMIT 5
                """)
                tool_usage = cursor.fetchall()

            return {
                "message_count": message_count,
                "reasoning_steps": reasoning_count,
                "tool_usage": dict(tool_usage),
                "avg_steps_per_conversation": reasoning_count / max(message_count // 2, 1)
            }
        except Exception as e:
            print(f"Error getting stats: {e}")
            return {
                "message_count": 0, 
                "reasoning_steps": 0, 
                "tool_usage": {}, 
                "avg_steps_per_conversation": 0
            }
