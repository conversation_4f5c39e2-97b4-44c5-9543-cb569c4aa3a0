#!/usr/bin/env python3
"""
Quick test to verify the Gemini agent tool fix
"""
import os
from dotenv import load_dotenv

if os.path.exists(".env"):
    load_dotenv()

from config.config import Config
from models import get_model_provider
from mcps.tools_manager import MCPToolsManager
from memory import ReActMemoryManager
from core.react_agent_gemini import ReActAgent as GeminiReActAgent

def test_gemini_tool_fix():
    """Test if the Gemini agent tool calling is now fixed."""
    
    print("🔧 TESTING GEMINI AGENT TOOL FIX")
    print("=" * 50)
    
    # Initialize components
    config = Config()
    model_provider = get_model_provider(config.model_provider)
    llm = model_provider.get_llm()
    
    tools_manager = MCPToolsManager()
    tools = tools_manager.get_tools()
    
    # Create fresh memory for clean test
    memory = ReActMemoryManager("test_gemini_fix.db")
    
    # Create Gemini agent
    agent = GeminiReActAgent(
        llm=llm,
        tools=tools,
        memory_manager=memory,
        max_iterations=3
    )
    
    # Test simple tool-based queries
    test_queries = [
        "What's today's date?",
        "Calculate 15 * 23",
        "What time is it?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🎯 Test {i}: '{query}'")
        print("-" * 40)
        
        try:
            response = agent.process(query)
            print(f"✅ Response: {response}")
            
            # Check if response contains actual data vs "25.0"
            if "25.0" in response and query != "What is 100 / 4?":
                print("⚠️ WARNING: Still getting '25.0' response")
            elif "Error:" in response:
                print("❌ ERROR: Tool execution failed")
            else:
                print("✅ SUCCESS: Tool appears to be working")
                
        except Exception as e:
            print(f"❌ EXCEPTION: {e}")
    
    print(f"\n{'='*50}")
    print("🔧 Tool fix test completed")

if __name__ == "__main__":
    test_gemini_tool_fix()
