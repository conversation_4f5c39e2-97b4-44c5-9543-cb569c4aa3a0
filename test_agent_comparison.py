#!/usr/bin/env python3
"""
Comprehensive comparison test between react_agent.py and react_agent_gemini.py

This test evaluates:
1. Response quality and accuracy
2. Tool usage effectiveness
3. Conversational ability
4. Performance metrics
5. Error handling
"""
import os
import time
import json
from typing import Dict, List, Any
from dotenv import load_dotenv

if os.path.exists(".env"):
    load_dotenv()

from config.config import Config
from models import get_model_provider
from mcps.tools_manager import MCPToolsManager
from memory import ReActMemoryManager

# Import both agent implementations
from core.react_agent import ReActAgent as OriginalReActAgent
from core.react_agent_gemini import ReActAgent as GeminiReActAgent


class AgentComparison:
    """Compare two ReAct agent implementations."""
    
    def __init__(self):
        """Initialize both agents with the same configuration."""
        
        # Initialize shared components
        self.config = Config()
        model_provider = get_model_provider(self.config.model_provider)
        self.llm = model_provider.get_llm()
        
        tools_manager = MCPToolsManager()
        self.tools = tools_manager.get_tools()
        
        # Create separate memory instances for each agent with fresh databases
        import os
        if os.path.exists("test_original_agent_fresh.db"):
            os.remove("test_original_agent_fresh.db")
        if os.path.exists("test_gemini_agent_fresh.db"):
            os.remove("test_gemini_agent_fresh.db")

        self.original_memory = ReActMemoryManager("test_original_agent_fresh.db")
        self.gemini_memory = ReActMemoryManager("test_gemini_agent_fresh.db")
        
        # Initialize both agents
        self.original_agent = OriginalReActAgent(
            llm=self.llm,
            tools=self.tools,
            memory_manager=self.original_memory,
            max_iterations=3
        )
        
        self.gemini_agent = GeminiReActAgent(
            llm=self.llm,
            tools=self.tools,
            memory_manager=self.gemini_memory,
            max_iterations=3
        )
        
        print("🔧 Both agents initialized successfully")
    
    def run_comparison_test(self) -> Dict[str, Any]:
        """Run comprehensive comparison tests."""
        
        print("🧪 REACT AGENT COMPARISON TEST")
        print("=" * 60)
        
        # Define test cases covering different scenarios
        test_cases = [
            {
                "category": "Tool Usage - Date/Time",
                "queries": [
                    "What's today's date?",
                    "What time is it?",
                    "What's the current date and time?"
                ]
            },
            {
                "category": "Tool Usage - Math",
                "queries": [
                    "Calculate 15 * 23",
                    "What is 100 / 4?",
                    "Solve 2 + 3 * 5"
                ]
            },
            {
                "category": "Conversational",
                "queries": [
                    "Hello, how are you?",
                    "Thank you for your help",
                    "Good morning!"
                ]
            },
            {
                "category": "Knowledge Questions",
                "queries": [
                    "What is the capital of France?",
                    "Who is the president of the United States?",
                    "What is the weather like in New York?"
                ]
            },
            {
                "category": "Mixed/Complex",
                "queries": [
                    "Can you tell me the time and calculate 10 + 5?",
                    "What's today's date? Also, what's 7 * 8?",
                    "Hello! What time is it?"
                ]
            }
        ]
        
        results = {
            "original_agent": {"total_score": 0, "categories": {}},
            "gemini_agent": {"total_score": 0, "categories": {}},
            "detailed_results": []
        }
        
        total_tests = 0
        
        for test_category in test_cases:
            category_name = test_category["category"]
            print(f"\n{'='*60}")
            print(f"📋 TESTING CATEGORY: {category_name}")
            print('='*60)
            
            category_results = {
                "original": {"score": 0, "responses": []},
                "gemini": {"score": 0, "responses": []}
            }
            
            for query in test_category["queries"]:
                total_tests += 1
                print(f"\n🎯 Query: '{query}'")
                print("-" * 40)
                
                # Test original agent
                original_result = self._test_single_agent(
                    self.original_agent, "Original", query
                )
                
                # Test gemini agent
                gemini_result = self._test_single_agent(
                    self.gemini_agent, "Gemini", query
                )
                
                # Score the responses
                original_score = self._score_response(query, original_result, category_name)
                gemini_score = self._score_response(query, gemini_result, category_name)
                
                category_results["original"]["score"] += original_score
                category_results["gemini"]["score"] += gemini_score
                
                category_results["original"]["responses"].append({
                    "query": query,
                    "result": original_result,
                    "score": original_score
                })
                category_results["gemini"]["responses"].append({
                    "query": query,
                    "result": gemini_result,
                    "score": gemini_score
                })
                
                # Show comparison
                print(f"📊 Scores - Original: {original_score}/10, Gemini: {gemini_score}/10")
                
                # Store detailed results
                results["detailed_results"].append({
                    "category": category_name,
                    "query": query,
                    "original": original_result,
                    "gemini": gemini_result,
                    "scores": {"original": original_score, "gemini": gemini_score}
                })
            
            # Category summary
            results["original_agent"]["categories"][category_name] = category_results["original"]
            results["gemini_agent"]["categories"][category_name] = category_results["gemini"]
            
            results["original_agent"]["total_score"] += category_results["original"]["score"]
            results["gemini_agent"]["total_score"] += category_results["gemini"]["score"]
            
            print(f"\n📈 Category '{category_name}' Summary:")
            print(f"   Original Agent: {category_results['original']['score']}/{len(test_category['queries']) * 10}")
            print(f"   Gemini Agent: {category_results['gemini']['score']}/{len(test_category['queries']) * 10}")
        
        # Final summary
        max_possible_score = total_tests * 10
        results["max_possible_score"] = max_possible_score
        results["total_tests"] = total_tests
        
        self._print_final_summary(results)
        
        return results
    
    def _test_single_agent(self, agent, agent_name: str, query: str) -> Dict[str, Any]:
        """Test a single agent with a query and return detailed results."""
        
        print(f"🤖 Testing {agent_name} Agent...")
        
        start_time = time.time()
        try:
            response = agent.process(query)
            response_time = time.time() - start_time
            success = True
            error = None
        except Exception as e:
            response = f"ERROR: {str(e)}"
            response_time = time.time() - start_time
            success = False
            error = str(e)
        
        result = {
            "agent": agent_name,
            "response": response,
            "response_time": response_time,
            "success": success,
            "error": error,
            "response_length": len(response) if response else 0
        }
        
        print(f"   ✅ Response ({response_time:.2f}s): {response[:100]}{'...' if len(response) > 100 else ''}")
        
        return result
    
    def _score_response(self, query: str, result: Dict[str, Any], category: str) -> int:
        """Score a response from 0-10 based on quality criteria."""
        
        if not result["success"]:
            return 0
        
        response = result["response"].lower()
        query_lower = query.lower()
        score = 0
        
        # Base score for successful response
        score += 2
        
        # Category-specific scoring
        if category == "Tool Usage - Date/Time":
            if "date" in query_lower:
                if "2025" in response and ("06" in response or "27" in response):
                    score += 6  # Correct current date
                elif any(year in response for year in ["2023", "2024", "2025"]):
                    score += 3  # At least has a reasonable year
            elif "time" in query_lower:
                if ":" in response and any(hour in response for hour in ["17", "18", "19", "20"]):
                    score += 6  # Reasonable current time format
                elif ":" in response:
                    score += 3  # At least time format
        
        elif category == "Tool Usage - Math":
            if "15 * 23" in query_lower and "345" in response:
                score += 6
            elif "100 / 4" in query_lower and "25" in response:
                score += 6
            elif "2 + 3 * 5" in query_lower and "17" in response:
                score += 6
            elif any(digit in response for digit in "0123456789"):
                score += 3  # At least contains numbers
        
        elif category == "Conversational":
            if any(greeting in response for greeting in ["hello", "hi", "good", "fine", "well", "thank"]):
                score += 6  # Appropriate conversational response
            elif len(response) > 10:
                score += 3  # At least some response
        
        elif category == "Knowledge Questions":
            if "capital of france" in query_lower and "paris" in response:
                score += 6
            elif "demo" in response or "search" in response:
                score += 4  # Used search tool appropriately
            elif len(response) > 20:
                score += 2  # At least attempted an answer
        
        # Response quality bonuses
        if result["response_time"] < 2.0:
            score += 1  # Fast response
        
        if 20 < len(result["response"]) < 200:
            score += 1  # Appropriate length
        
        return min(score, 10)  # Cap at 10
    
    def _print_final_summary(self, results: Dict[str, Any]):
        """Print comprehensive final summary."""
        
        print(f"\n{'='*60}")
        print("🏆 FINAL COMPARISON RESULTS")
        print('='*60)
        
        original_total = results["original_agent"]["total_score"]
        gemini_total = results["gemini_agent"]["total_score"]
        max_score = results["max_possible_score"]
        
        print(f"📊 Overall Scores:")
        print(f"   Original Agent: {original_total}/{max_score} ({original_total/max_score*100:.1f}%)")
        print(f"   Gemini Agent:   {gemini_total}/{max_score} ({gemini_total/max_score*100:.1f}%)")
        
        if original_total > gemini_total:
            winner = "Original Agent"
            margin = original_total - gemini_total
        elif gemini_total > original_total:
            winner = "Gemini Agent"
            margin = gemini_total - original_total
        else:
            winner = "TIE"
            margin = 0
        
        print(f"\n🥇 Winner: {winner}")
        if margin > 0:
            print(f"   Margin: {margin} points ({margin/max_score*100:.1f}%)")
        
        # Category breakdown
        print(f"\n📋 Category Breakdown:")
        for category in results["original_agent"]["categories"]:
            orig_score = results["original_agent"]["categories"][category]["score"]
            gem_score = results["gemini_agent"]["categories"][category]["score"]
            print(f"   {category}:")
            print(f"      Original: {orig_score}, Gemini: {gem_score}")
        
        # Recommendations
        print(f"\n💡 Recommendations:")
        if original_total > gemini_total:
            print("   ✅ Original Agent performs better overall")
            print("   💡 The hybrid rule-based + LLM approach is more effective")
        elif gemini_total > original_total:
            print("   ✅ Gemini Agent performs better overall")
            print("   💡 The traditional ReAct approach is more effective")
        else:
            print("   ⚖️ Both agents perform similarly")
            print("   💡 Consider other factors like maintainability and extensibility")


def main():
    """Run the agent comparison test."""
    
    comparison = AgentComparison()
    results = comparison.run_comparison_test()
    
    # Save results to file
    with open("agent_comparison_results.json", "w") as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Detailed results saved to 'agent_comparison_results.json'")


if __name__ == "__main__":
    main()
